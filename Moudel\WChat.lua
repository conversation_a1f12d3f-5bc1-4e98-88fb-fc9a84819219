-- 统一配置和常量
local CHANNEL_CONFIG = {
    DEFAULT_CHANNELS = {"综合", "交易", "寻求组队", "大脚世界频道"},
    PATTERNS = {
        "%[(%d+)%. 综合.-%]", "%[(%d+)%. 交易.-%]", "%[(%d+)%. 本地防务.-%]",
        "%[(%d+)%. 寻求组队%]", "%[(%d+)%. 世界防务%]", "%[(%d+)%. 公会招募.-%]",
        "%[(%d+)%. 大脚世界频道.-%]"
    },
    REPLACEMENTS = {"[%1.综]", "[%1.交]", "[%1.防]", "[%1.组]", "[%1.守]", "[%1.招]", "[%1.世]"},
    MAPPINGS = {chnGen = "综合", chnTrade = "交易", chnLFG = "寻求组队", world = "大脚世界频道"}
}

-- WChat主体和配置
local WChat = {
    DefaultConfig = {
        UseTopInput = true, UseVertical = false, DistanceVertical = 24, DistanceHorizontal = 24,
        AlphaOnLeave = 1.0, EmoteIconListSize = 30, ButtonSize = 22, EnableEmoteInput = true,
        Position = nil, LockChatBar = true, HiddenChannels = {}, EnableChannelShortNames = true,
        EnableTimestamp = true, TimestampColor = {r = 255, g = 20, b = 147}, TimestampFormat = "[%H:%M:%S]"
    },
    registeredFilters = {}
}

-- 基础工具函数（避免循环依赖）
local function DeepCopy(t)
    if type(t) ~= "table" then return t end
    local c = {}
    for k, v in pairs(t) do c[k] = DeepCopy(v) end
    return c
end

-- 配置管理（简化版）
local function GetConfig()
    if not WChatClassicDB then WChatClassicDB = {} end
    for key, defaultValue in pairs(WChat.DefaultConfig) do
        if WChatClassicDB[key] == nil then
            WChatClassicDB[key] = DeepCopy(defaultValue)
        elseif key == "TimestampColor" and type(WChatClassicDB[key]) == "table" then
            local c = WChatClassicDB[key]
            if (c.r and c.g and c.b and c.r <= 1) or (c.r == 255 and c.g == 182 and c.b == 193) then
                WChatClassicDB[key] = DeepCopy(defaultValue)
            end
        elseif key == "TimestampFormat" and WChatClassicDB[key] == "%H:%M:%S" then
            WChatClassicDB[key] = "[%H:%M:%S]"
        end
    end
    return WChatClassicDB
end

-- 统一工具函数
local Utils = {
    GetConfigSafe = function() return pcall(GetConfig) and GetConfig() or nil end,
    -- 统一的按钮鼠标事件设置
    SetButtonHoverEvents = function(button, enterAlpha, leaveAlpha, tooltip)
        button:SetScript("OnEnter", function(self)
            if enterAlpha then self:SetAlpha(enterAlpha) end
            if tooltip then
                GameTooltip:SetOwner(self, "ANCHOR_RIGHT")
                GameTooltip:SetText(tooltip)
                GameTooltip:Show()
            end
        end)
        button:SetScript("OnLeave", function(self)
            if leaveAlpha then self:SetAlpha(leaveAlpha) end
            GameTooltip:Hide()
        end)
    end,
    -- 统一的事件注册
    RegisterEvents = function(frame, events)
        for _, event in ipairs(events) do frame:RegisterEvent(event) end
    end,
    -- 统一的频道处理
    ProcessChannels = function(channels, callback)
        for _, channelName in ipairs(channels) do callback(channelName) end
    end,
    -- 统一的频道添加/移除处理（消除重复逻辑）
    ProcessDefaultChannels = function(includeHiddenCheck)
        local config = includeHiddenCheck and GetConfig() or nil
        for _, channelName in ipairs(CHANNEL_CONFIG.DEFAULT_CHANNELS) do
            local channelId = GetChannelName(channelName)
            if channelId and channelId > 0 then
                if includeHiddenCheck then
                    -- 完整版本：考虑用户屏蔽设置
                    if not config.HiddenChannels[channelName] and not IsChannelShown(channelName) then
                        ChatFrame_AddChannel(DEFAULT_CHAT_FRAME, channelName)
                    elseif config.HiddenChannels[channelName] and IsChannelShown(channelName) then
                        ChatFrame_RemoveChannel(DEFAULT_CHAT_FRAME, channelName)
                    end
                else
                    -- 简化版本：只添加未显示的频道
                    if not IsChannelShown(channelName) then
                        ChatFrame_AddChannel(DEFAULT_CHAT_FRAME, channelName)
                    end
                end
            end
        end
        UpdateChannelXIcons()
    end,
    -- 统一的字符串检查
    HasMarkup = function(text, markup) return text and string.find(text, markup) end,
    -- 统一的聊天框处理
    ProcessChatFrames = function(callback, skipFrame2)
        for i = 1, NUM_CHAT_WINDOWS do
            if not skipFrame2 or i ~= 2 then
                local chatFrame = _G['ChatFrame' .. i]
                if chatFrame then callback(chatFrame, i) end
            end
        end
    end
}

-- 聊天条工具函数（提前声明）
local ChatBarUtils = {
    savePosition = function()
        local chatBar = WChat.ChatBar
        if chatBar then
            local config = GetConfig()
            local point, relativeTo, relativePoint, x, y = chatBar:GetPoint()
            -- 保存完整的位置信息，包括relativeTo
            config.Position = {
                point = point,
                relativeTo = relativeTo and relativeTo:GetName() or "UIParent",
                relativePoint = relativePoint,
                x = x,
                y = y
            }
        end
    end,
    setBackdrop = function(movable)
        local chatBar = WChat.ChatBar
        if chatBar then
            if movable then
                -- 解锁状态：显示绿色背景
                chatBar:SetBackdropColor(0, 1, 0, 0.3)
                chatBar:SetBackdropBorderColor(0, 1, 0, 0.8)
            else
                -- 锁定状态：完全透明，不显示背景
                chatBar:SetBackdropColor(0, 0, 0, 0)
                chatBar:SetBackdropBorderColor(0, 0, 0, 0)
            end
        end
    end
}

-- 聊天条拖拽设置函数（将在后面定义）

local function InitializeConfig()
    GetConfig()
    WChat.db = { profile = WChatClassicDB }
end
-- 事件过滤器管理（简化版）
function WChat:RegisterFilter(event, callback)
    ChatFrame_AddMessageEventFilter(event, callback)
    if not self.registeredFilters[event] then self.registeredFilters[event] = {} end
    table.insert(self.registeredFilters[event], callback)
end

function WChat:UnregisterAllFilters()
    for event, callbacks in pairs(self.registeredFilters) do
        for _, callback in ipairs(callbacks) do ChatFrame_RemoveMessageEventFilter(event, callback) end
    end
    self.registeredFilters = {}
end

-- 密语记录模块（提前声明）
WhisperRecord = {}

-- 配置常量（提前声明）
local CONFIG = {
    BNET_CLASS_ID = "BN_2",
    TIME = {
        AUTO_HIDE_DELAY = 10,
        HOURS_PER_DAY = 24,
        MINUTES_PER_HOUR = 60,
        SECONDS_PER_MINUTE = 60,
        UI_UPDATE_DELAY = 0.1  -- 统一的UI更新延迟时间
    },
    COLORS = {
        BNET_FRIEND = {r = 0, g = 1, b = 0.9647, a = 1},
        TITLE = {r = 1, g = 0.843, b = 0, a = 1},
        DIVIDER = {r = 1, g = 0.82, b = 0, a = 0.6},
        CHANNEL_DEFAULT = {0.82, 0.70, 0.55}  -- 统一的频道按钮颜色
    },
    UI = {
        MAX_LINES = 100,
        BACKDROP_ALPHA = 0.66,
        SETTINGS_BACKDROP_ALPHA = 0.8,
        CHAT_CONTENT_BACKDROP_ALPHA = 0.8,
        LIST_ITEM_BG_ALPHA = 0.1,
        LIST_ITEM_HIGHLIGHT_ALPHA = 0.3,
        SCROLLBAR_SCALE = 0.75
    },
    MAIN_FRAME = {
        name = "WChatClassic_WhisperRecord",
        width = 220,
        height = 310,
        itemHeight = 22,
    },
}

-- 统一事件处理器
local EventHandler = {
    frame = nil,
    Register = function(self)
        if self.frame then return end
        self.frame = CreateFrame("Frame")
        local events = {"CHAT_MSG_WHISPER", "CHAT_MSG_WHISPER_INFORM", "CHAT_MSG_BN_WHISPER", "CHAT_MSG_BN_WHISPER_INFORM"}
        for _, event in ipairs(events) do self.frame:RegisterEvent(event) end
        self.frame:SetScript("OnEvent", function(_, event, ...)
            if not WhisperRecord then return end
            if event:find("WHISPER") and not event:find("INFORM") then
                if WhisperRecord.RecordWhisperMessage then WhisperRecord.RecordWhisperMessage(event, ...) end
            elseif event:find("INFORM") then
                local message, target, guid = select(1, ...), select(2, ...), select(12, ...)
                local isBNet = event:find("BN_") ~= nil
                if WhisperRecord.RecordSentWhisper then WhisperRecord.RecordSentWhisper(message, target, guid, isBNet) end
            end
        end)
    end,
    Unregister = function(self)
        if self.frame then
            self.frame:UnregisterAllEvents()
            self.frame:SetScript("OnEvent", nil)
            self.frame = nil
        end
    end
}

-- 定时器管理（简化版）
local WhisperTimers = {
    timers = {},
    Clear = function(self)
        for _, timer in pairs(self.timers) do
            if timer then timer:Cancel() end
        end
        self.timers = {}
    end,
    Set = function(self, name, timer)
        if self.timers[name] then self.timers[name]:Cancel() end
        self.timers[name] = timer
    end
}

local function InitializeWhisperRecord()
    if not WhisperRecord or not WhisperRecord.CreateWhisperRecordFrame then return end
    WhisperRecord.CreateWhisperRecordFrame()
    EventHandler:Register()
    WhisperTimers:Clear()

    WhisperTimers:Set("classInfo", C_Timer.NewTicker(1, function()
        if WhisperRecord.Utils and WhisperRecord.Utils.ClassInfo then
            WhisperRecord.Utils.ClassInfo:Process()
        end
    end))
end

-- 聊天条拖拽设置函数（需要在配置处理器之前定义）
local function SetupChatBarDragging(chatBar, enabled)
    if not chatBar then return end
    chatBar:SetMovable(enabled)
    chatBar:EnableMouse(enabled)
    if enabled then
        chatBar:RegisterForDrag("LeftButton")
        chatBar:SetScript("OnDragStart", chatBar.StartMoving)
        chatBar:SetScript("OnDragStop", function(self)
            self:StopMovingOrSizing()
            ChatBarUtils.savePosition()
        end)
    else
        chatBar:SetScript("OnDragStart", nil)
        chatBar:SetScript("OnDragStop", nil)
    end
end

-- 配置变化处理（统一简化版）
WChat.OnConfigChanged = {}
local function HandleConfigChange(key, value, customHandler)
    print("=== HandleConfigChange 调试 ===")
    print("key:", key, "value:", value)
    print("设置前 GetConfig()[key]:", GetConfig()[key])
    GetConfig()[key] = value
    print("设置后 GetConfig()[key]:", GetConfig()[key])
    if customHandler then
        print("调用自定义处理器...")
        customHandler(value)
        print("自定义处理器调用完成")
    elseif WChat.InitChatBar then
        print("调用 WChat:InitChatBar()...")
        WChat:InitChatBar()
        print("WChat:InitChatBar() 调用完成")
    end
    print("=== HandleConfigChange 调试结束 ===")
end

-- 批量创建配置处理器
local configHandlers = {
    UseVertical = function(v) if WChat.InitChatBar then WChat:InitChatBar(); if WChat.ChatBar then WChat.ChatBar:Show() end end end,
    UseTopInput = function(v)
        if _G.ElvUI then return end
        local editBox = DEFAULT_CHAT_FRAME.editBox
        if editBox then
            editBox:ClearAllPoints()
            local point = v and "BOTTOMLEFT" or "TOPLEFT"
            local relativePoint = v and "TOPLEFT" or "BOTTOMLEFT"
            local yOffset = v and 20 or -5
            editBox:SetPoint(point, DEFAULT_CHAT_FRAME, relativePoint, 0, yOffset)
            editBox:SetPoint(point:gsub("LEFT", "RIGHT"), DEFAULT_CHAT_FRAME, relativePoint:gsub("LEFT", "RIGHT"), 0, yOffset)
        end
    end,
    EmoteIconListSize = function() if WChat.InitEmoteTableFrame then WChat:InitEmoteTableFrame() end end,
    LockChatBar = function(v)
        print("=== 配置处理器调试 ===")
        print("接收到的值 v:", v)
        print("WChat.ChatBar 存在:", WChat.ChatBar ~= nil)
        if WChat.ChatBar then
            print("调用 SetupChatBarDragging, 参数:", not v)
            SetupChatBarDragging(WChat.ChatBar, not v)
            print("调用 ChatBarUtils.setBackdrop, 参数:", not v)
            ChatBarUtils.setBackdrop(not v)
            print("配置处理器执行完成")
        else
            print("警告: WChat.ChatBar 不存在!")
        end
        print("=== 配置处理器调试结束 ===")
    end,
    ResetToDefaults = function()
        WChatClassicDB = nil
        GetConfig()
        if WChat.InitChatBar then WChat:InitChatBar() end
        if WChat.InitEmoteTableFrame then WChat:InitEmoteTableFrame() end
        -- 重置后不自动添加任何频道，完全由用户控制
    end
}

-- 频道缩写和时间戳处理
local ChannelShortener = {
    ProcessMessage = function(self, text, ...)
        local config = Utils.GetConfigSafe()
        if not config then return self.OriginalAddMessage(self, text, ...) end
        text = tostring(text or "")

        -- 频道缩写处理
        if config.EnableChannelShortNames then
            for i = 1, #CHANNEL_CONFIG.PATTERNS do
                text = text:gsub(CHANNEL_CONFIG.PATTERNS[i], CHANNEL_CONFIG.REPLACEMENTS[i])
            end
            text = text:gsub("%[(%d+)%. .-%]", "%1.")
        end

        -- 时间戳处理
        if config.EnableTimestamp then
            local timeColor = config.TimestampColor or {r = 255, g = 20, b = 147}
            local r, g, b = timeColor.r > 1 and timeColor.r or timeColor.r * 255,
                           timeColor.g > 1 and timeColor.g or timeColor.g * 255,
                           timeColor.b > 1 and timeColor.b or timeColor.b * 255
            local colorCode = string.format("ff%02x%02x%02x",
                math.min(255, math.max(0, math.floor(r))),
                math.min(255, math.max(0, math.floor(g))),
                math.min(255, math.max(0, math.floor(b))))

            local timestamp = date(config.TimestampFormat or "[%H:%M:%S]")
            -- 生成唯一的消息ID：时间戳 + 内容哈希 + 随机数
            local currentTime = GetTime()
            local contentHash = text:gsub("|[cr].-", ""):gsub("[|:]", ""):sub(1, 30)
            local randomSuffix = math.random(1000, 9999)
            local messageId = string.format("%.3f_%s_%d", currentTime, contentHash, randomSuffix)
            local clickableTimestamp = "|Hwchat:copy:" .. messageId .. "|h|c" .. colorCode .. timestamp .. "|r|h "

            text = clickableTimestamp .. text

            -- 消息缓存管理
            if not WChat.messageCache then WChat.messageCache, WChat.messageCacheOrder = {}, {} end
            if not WChat.messageCache[messageId] then
                if #WChat.messageCacheOrder >= 100 then
                    WChat.messageCache[table.remove(WChat.messageCacheOrder, 1)] = nil
                end
                WChat.messageCache[messageId] = text:gsub("|H.-|h", ""):gsub("|[cr].-", "")
                table.insert(WChat.messageCacheOrder, messageId)
            end
        end

        return self.OriginalAddMessage(self, text, ...)
    end,

    Initialize = function(self)
        local config = GetConfig()
        self:Cleanup()
        if config.EnableChannelShortNames or config.EnableTimestamp then
            Utils.ProcessChatFrames(function(chatFrame, i)
                if chatFrame.AddMessage then
                    chatFrame.OriginalAddMessage = chatFrame.AddMessage
                    chatFrame.AddMessage = self.ProcessMessage
                end
            end, true)
        end
    end,

    Cleanup = function(self)
        Utils.ProcessChatFrames(function(chatFrame, i)
            if chatFrame.OriginalAddMessage then
                chatFrame.AddMessage = chatFrame.OriginalAddMessage
                chatFrame.OriginalAddMessage = nil
            end
        end)
    end
}

-- 批量设置配置处理器（在ChannelShortener定义之后）
local configGroups = {
    {{"DistanceHorizontal", "DistanceVertical", "AlphaOnLeave", "ButtonSize"}, function(v) if WChat.InitChatBar then WChat:InitChatBar() end end},
    {{"EnableEmoteInput"}, function(v) if WChat.InitEmoteTableFrame then WChat:InitEmoteTableFrame() end end},
    {{"EnableTimestamp", "EnableChannelShortNames", "TimestampColor", "TimestampFormat"}, function(v) ChannelShortener:Initialize() end}
}
for _, group in ipairs(configGroups) do
    Utils.ProcessChannels(group[1], function(key) configHandlers[key] = group[2] end)
end

for key, handler in pairs(configHandlers) do
    WChat.OnConfigChanged[key] = function(value) HandleConfigChange(key, value, handler) end
end

-- WChat模块清理函数（简化版）
function WChat:Cleanup()
    -- 清理定时器
    WhisperTimers:Clear()
    for _, timerName in ipairs({"resetTimer", "whisperInitTimer", "elvuiTimer"}) do
        if self[timerName] then self[timerName]:Cancel(); self[timerName] = nil end
    end

    -- 清理框架
    if self.elvuiFrame then
        self.elvuiFrame:UnregisterAllEvents()
        self.elvuiFrame:SetScript("OnEvent", nil)
        self.elvuiFrame = nil
    end

    -- 清理各种处理器
    EventHandler:Unregister()
    self:UnregisterAllFilters()
    ChannelShortener:Cleanup()

    -- 清理聊天条
    if self.ChatBar then
        self.ChatBar:UnregisterAllEvents()
        self.ChatBar:SetScript("OnEvent", nil)
    end

    -- 清理UI框架
    local framesToClean = {
        {EmoteTableFrame, function(f)
            if f.clickCatcher then f.clickCatcher:Hide(); f.clickCatcher:SetParent(nil); f.clickCatcher = nil end
            f:SetParent(nil)
        end},
        {chatCopyFrame, function(f) f:SetParent(nil) end},
        {WhisperRecord and WhisperRecord.WanMY, function(f) f:SetParent(nil); WhisperRecord.WanMY = nil end}
    }

    for _, frameData in ipairs(framesToClean) do
        local frame, cleanFunc = frameData[1], frameData[2]
        if frame then frame:Hide(); cleanFunc(frame) end
    end

    -- 清理缓存和标志
    self.messageCache, self.messageCacheOrder, self.emoteEventsRegistered = nil, nil, false
end

-- WChat模块初始化函数（简化版）
local function InitWChatModule()
    if _G.WChat and _G.WChat.Cleanup then _G.WChat:Cleanup() end

    InitializeConfig()
    ChannelShortener:Initialize()
    _G.WChat = WChat

    -- 延迟初始化，确保所有函数都已定义
    C_Timer.After(CONFIG.TIME.UI_UPDATE_DELAY, function()
        WChat:InitChatBar()
        WChat:InitEmoteTableFrame()
        WChat:InitChatSkin()
    end)

    WChat.whisperInitTimer = C_Timer.NewTimer(1, InitializeWhisperRecord)

    local function ApplyElvUIFix()
        if not _G.ElvUI then return end
        local E = unpack(_G.ElvUI)
        local CH = E:GetModule('Chat')
        if not CH then return end
        if CH.StyleChat then
            for _, name in ipairs(_G.CHAT_FRAMES) do
                local frame = _G[name]
                if frame then CH:StyleChat(frame) end
            end
        end
        if CH.UpdateEditboxAnchors then CH:UpdateEditboxAnchors() end
    end

    if _G.ElvUI then
        WChat.elvuiTimer = C_Timer.NewTimer(2, ApplyElvUIFix)
    else
        WChat.elvuiFrame = CreateFrame("Frame")
        WChat.elvuiFrame:RegisterEvent("ADDON_LOADED")
        WChat.elvuiFrame:SetScript("OnEvent", function(self, event, addonName)
            if addonName == "ElvUI" then
                WChat.elvuiTimer = C_Timer.NewTimer(1, ApplyElvUIFix)
                self:UnregisterEvent("ADDON_LOADED")
                WChat.elvuiFrame = nil
            end
        end)
    end
end

if WanTiny_RegisterModule then WanTiny_RegisterModule("WChat", InitWChatModule) end

-- 聊天条功能
-- 常量定义（合并简化）
local PATHS = {
    EMOTION = "Interface/AddOns/WanTiny/Textures/Emotion",
    TEXTURE = "Interface/AddOns/WanTiny/Textures"
}

local CLASS_COLORS = {
    WARRIOR = {r = 0.78, g = 0.61, b = 0.43}, PALADIN = {r = 0.96, g = 0.55, b = 0.73},
    HUNTER = {r = 0.67, g = 0.83, b = 0.45}, ROGUE = {r = 1.0, g = 0.96, b = 0.41},
    PRIEST = {r = 1.0, g = 1.0, b = 1.0}, SHAMAN = {r = 0.0, g = 0.44, b = 0.87},
    MAGE = {r = 0.41, g = 0.8, b = 0.94}, WARLOCK = {r = 0.58, g = 0.51, b = 0.79},
    DRUID = {r = 1.0, g = 0.49, b = 0.04}, DEATHKNIGHT = {r = 0.77, g = 0.12, b = 0.23},
    BN_2 = {r = 0, g = 1, b = 0.9647}
}

local CLASS_ICON_COORDS = {
    WARRIOR = {0, 0.25, 0, 0.25}, MAGE = {0.25, 0.49609375, 0, 0.25},
    ROGUE = {0.49609375, 0.7421875, 0, 0.25}, DRUID = {0.7421875, 0.98828125, 0, 0.25},
    HUNTER = {0, 0.25, 0.25, 0.5}, SHAMAN = {0.25, 0.49609375, 0.25, 0.5},
    PRIEST = {0.49609375, 0.7421875, 0.25, 0.5}, WARLOCK = {0.7421875, 0.98828125, 0.25, 0.5},
    PALADIN = {0, 0.25, 0.5, 0.75}, DEATHKNIGHT = {0.25, 0.49609375, 0.5, 0.75}
}

local function CreateTooltip(action, rightAction)
    local base = "|cff00ffff鼠标左键|r-|cffff80ff" .. action .. "|r"
    return rightAction and (base .. "\n|cff00ffff鼠标右键|r-|cffff80ff" .. rightAction .. "|r") or base
end

-- MeetingHorn工具提示（简化版）
local function CreateMeetingHornTooltip()
    local tooltip = "|cff00ffff鼠标左键|r-|cffff80ff打开集结号|r"
    if not IsAddOnLoaded("MeetingHorn") then
        return tooltip .. "\n|cffff8080插件未加载|r"
    end

    local addon = LibStub and LibStub('AceAddon-3.0'):GetAddon('MeetingHorn', true)
    if not addon then return tooltip .. "\n|cffff8080插件实例未找到|r" end

    local lfg = addon:GetModule('LFG', true)
    if not lfg then return tooltip .. "\n|cffff8080LFG模块未找到|r" end

    local icon1 = "|TInterface\\AddOns\\MeetingHorn\\Media\\DataBroker:16:16:0:0:64:32:0:32:0:32|t"
    local icon2 = "|TInterface\\AddOns\\MeetingHorn\\Media\\DataBroker:16:16:0:0:64:32:32:64:0:32|t"
    tooltip = tooltip .. "\n" .. icon2 .. "|cffFFD700活动数量: " .. lfg:GetActivityCount() .. "|r"

    local count = lfg:GetCurrentActivity() and lfg:GetApplicantCount() or lfg:GetApplicationCount()
    local label = lfg:GetCurrentActivity() and "申请者数量" or "申请数量"
    return tooltip .. "\n" .. icon1 .. "|cffFFD700" .. label .. ": " .. count .. "|r"
end

-- 工具提示配置（简化版）
local TOOLTIPS = {
    emote = CreateTooltip("打开表情面板"), say = CreateTooltip("说话"), yell = CreateTooltip("大喊"),
    party = CreateTooltip("小队聊天"), guild = CreateTooltip("公会聊天"), raid = CreateTooltip("团队聊天"),
    bg = CreateTooltip("战场/副本聊天"), roll = CreateTooltip("随机Roll点", "打开战利品投骰界面"),
    gold = CreateTooltip("打开金团表"), ai = CreateTooltip("打开AI助手"),
    atlas = CreateTooltip("打开副本掉落/AtlasLoot"), chatcopy = CreateTooltip("聊天内容复制"),
    minimize = CreateTooltip("聊天窗口最小化/还原"), whisper = CreateTooltip("密语记录/未读高亮"),
    meetinghorn = CreateMeetingHornTooltip
}

-- 频道按钮工具提示
for _, channel in ipairs({"chnGen", "chnTrade", "chnLFG", "world"}) do
    TOOLTIPS[channel] = CreateTooltip("加入/发言", "切换频道消息显示/屏蔽")
end

-- 统一工具函数（扩展Utils）
function Utils.SetStandardBackdrop(frame, bgAlpha, borderAlpha, backdropType)
    local backdrops = {
        default = {bgFile = "Interface/Tooltips/UI-Tooltip-Background", edgeFile = "Interface/Tooltips/UI-Tooltip-Border"},
        emote = {bgFile = "Interface\\Buttons\\WHITE8x8", edgeFile = "Interface/Tooltips/UI-Tooltip-Border"},
        dialog = {bgFile = "Interface/DialogFrame/UI-DialogBox-Background", edgeFile = "Interface/DialogFrame/UI-DialogBox-Border"}
    }

    local backdrop = backdrops[backdropType] or backdrops.default
    backdrop.tile, backdrop.tileSize, backdrop.edgeSize = true, 16, 16
    backdrop.insets = backdropType == "emote" and {left = 3, right = 3, top = 3, bottom = 3} or {left = 4, right = 4, top = 4, bottom = 4}

    if not frame.SetBackdrop then Mixin(frame, BackdropTemplateMixin) end
    frame:SetBackdrop(backdrop)
    frame:SetBackdropColor(0, 0, 0, bgAlpha or 0.8)
    frame:SetBackdropBorderColor(0.5, 0.5, 0.5, borderAlpha or 1)
end

function Utils.GetClassColorCode(className)
    local c = CLASS_COLORS[className]
    return c and string.format("|cff%02x%02x%02x", c.r*255, c.g*255, c.b*255) or "|cffffffff"
end

function Utils.GetDisplayName(playerName, keepRealm)
    if not playerName or string.find(playerName, "#") then return playerName or "" end
    return keepRealm and playerName or (strsplit("-", playerName) or playerName)
end

local IsMovable = false
local ChatBar = CreateFrame("Frame", nil, UIParent, "BackdropTemplate")

-- 设置ChatBar的背景结构（但不显示，只在解锁时显示）
ChatBar:SetBackdrop({
    bgFile = "Interface/ChatFrame/ChatFrameBackground",
    edgeFile = "Interface/ChatFrame/ChatFrameBackground",
    edgeSize = 1
})
-- 初始状态：锁定时不显示背景
ChatBar:SetBackdropColor(0, 0, 0, 0)
ChatBar:SetBackdropBorderColor(0, 0, 0, 0)

WChat.ChatBar = ChatBar

-- 频道处理函数（简化版）
local function HandleChannelAction(channelName, openChat)
    local chnId = GetChannelName(channelName)
    if not chnId or chnId == 0 then
        JoinPermanentChannel(channelName)
        ChatFrame_AddChannel(DEFAULT_CHAT_FRAME, channelName)
        if openChat then
            C_Timer.After(0.2, function()
                local newId = GetChannelName(channelName)
                if newId and newId > 0 then
                    ChatFrame_OpenChat("/" .. newId .. " " .. DEFAULT_CHAT_FRAME.editBox:GetText(), DEFAULT_CHAT_FRAME)
                end
            end)
        end
    elseif openChat then
        ChatFrame_OpenChat("/" .. chnId .. " " .. DEFAULT_CHAT_FRAME.editBox:GetText(), DEFAULT_CHAT_FRAME)
    else
        LeaveChannelByName(channelName)
    end
end

local function OpenChatWithCommand(command)
    ChatFrame_OpenChat(command .. " " .. DEFAULT_CHAT_FRAME.editBox:GetText(), DEFAULT_CHAT_FRAME)
end

function IsChannelShown(channelName)
    local channels = {GetChatWindowChannels(DEFAULT_CHAT_FRAME:GetID() or 1)}
    for i = 1, #channels, 2 do
        if channels[i] == channelName then return true end
    end
    return false
end

local function ToggleChannelShowHide(channelName)
    local config = GetConfig()
    local isShown = IsChannelShown(channelName)
    if isShown then
        ChatFrame_RemoveChannel(DEFAULT_CHAT_FRAME, channelName)
        config.HiddenChannels[channelName] = true
    else
        ChatFrame_AddChannel(DEFAULT_CHAT_FRAME, channelName)
        config.HiddenChannels[channelName] = nil
    end
end

function UpdateChannelXIcons()
    if not ChatBar then return end
    for _, child in pairs({ChatBar:GetChildren()}) do
        if child.X and child.buttonName and CHANNEL_CONFIG.MAPPINGS[child.buttonName] then
            local channelName = CHANNEL_CONFIG.MAPPINGS[child.buttonName]
            child.X:SetShown(not IsChannelShown(channelName))
        end
    end
end

-- 点击处理器（统一简化）
local ClickHandlers = {
    -- 基础频道
    say = function() OpenChatWithCommand("/s") end,
    yell = function() OpenChatWithCommand("/y") end,
    party = function() OpenChatWithCommand("/p") end,
    guild = function() OpenChatWithCommand("/g") end,
    raid = function() OpenChatWithCommand("/raid") end,
    bg = function()
        local inBG = UnitInBattleground("player")
        local inInstance, instanceType = IsInInstance()
        if inBG then
            OpenChatWithCommand("/bg")
        elseif inInstance and (instanceType == "party" or instanceType == "raid") then
            OpenChatWithCommand("/i")
        else
            -- 在非战场/副本环境下给出提示
            print("|cffff8000WChat|r: 当前不在战场或副本中")
        end
    end,

    -- 频道处理器将在下面动态生成，避免重复定义

    -- 特殊功能
    emote = function() if WChat.ToggleEmoteTable then WChat:ToggleEmoteTable() end end,
    roll = function(self, button)
        if button == "RightButton" then ToggleLootHistoryFrame() else RandomRoll(1, 100) end
    end,
    chatcopy = function() if WChat.CopyFunc then WChat:CopyFunc() end end
}

-- 频道切换处理器
Utils.ProcessChannels(CHANNEL_CONFIG.DEFAULT_CHANNELS, function(channelName)
    for key, value in pairs(CHANNEL_CONFIG.MAPPINGS) do
        if value == channelName then
            ClickHandlers[key] = function(self, button)
                if button == "LeftButton" then
                    -- 左键：如果频道未显示则加入并打开聊天，如果已显示则直接打开聊天
                    if not IsChannelShown(channelName) then
                        ToggleChannelShowHide(channelName)
                        C_Timer.After(CONFIG.TIME.UI_UPDATE_DELAY, UpdateChannelXIcons)
                        -- 延迟打开聊天，确保频道已加入
                        C_Timer.After(0.3, function()
                            local chnId = GetChannelName(channelName)
                            if chnId and chnId > 0 then
                                ChatFrame_OpenChat("/" .. chnId .. " " .. DEFAULT_CHAT_FRAME.editBox:GetText(), DEFAULT_CHAT_FRAME)
                            end
                        end)
                    else
                        -- 频道已显示，直接打开聊天
                        local chnId = GetChannelName(channelName)
                        if chnId and chnId > 0 then
                            ChatFrame_OpenChat("/" .. chnId .. " " .. DEFAULT_CHAT_FRAME.editBox:GetText(), DEFAULT_CHAT_FRAME)
                        end
                    end
                else
                    -- 右键：切换频道显示/隐藏
                    ToggleChannelShowHide(channelName)
                    C_Timer.After(CONFIG.TIME.UI_UPDATE_DELAY, UpdateChannelXIcons)
                end
            end
            break
        end
    end
end)



local function Movelock_OnClick(self, button)
    if button == "LeftButton" then
        print("=== 移动锁按钮调试 ===")
        print("点击前 IsMovable:", IsMovable)
        print("点击前 config.LockChatBar:", GetConfig().LockChatBar)

        IsMovable = not IsMovable
        local newLockState = not IsMovable

        print("点击后 IsMovable:", IsMovable)
        print("新的 LockChatBar 值:", newLockState)

        -- 调用配置变更处理器，这样配置界面也会同步更新
        if WChat.OnConfigChanged and WChat.OnConfigChanged.LockChatBar then
            print("调用配置处理器...")
            WChat.OnConfigChanged.LockChatBar(newLockState)
            print("配置处理器调用完成")
        else
            print("警告: 配置处理器不存在!")
        end

        print("最终 config.LockChatBar:", GetConfig().LockChatBar)
        print("=== 调试结束 ===")

        if not IsMovable then ChatBarUtils.savePosition() end
    elseif button == "MiddleButton" and IsMovable then
        ChatBar:ClearAllPoints()
        ChatBar:SetPoint("BOTTOMLEFT", UIParent, "BOTTOMLEFT", 0, 0)
    end
end

local BGButtonFrame

-- 改为全局函数，避免作用域问题
function UpdateBGButtonText()
    if not BGButtonFrame or not BGButtonFrame.text then return end
    local inBG = UnitInBattleground("player")
    BGButtonFrame.text:SetText(inBG and "战" or "副")
end

-- 改为全局函数，避免作用域问题
function UpdateMinimizeButtonText()
    if not ChatBar or not ChatBar.MinimizeBtn then return end
    local btn = _G.WChat_ChatHideButton
    local shown = _G.ChatFrame1 and _G.ChatFrame1:IsVisible()
    if shown then
        ChatBar.MinimizeBtn.text:SetText("隐")
    else
        ChatBar.MinimizeBtn.text:SetText("显")
    end
end

local function ChatMinimize_OnClick(self, button)
    local btn = _G.WChat_ChatHideButton
    if btn and btn:GetScript("OnClick") then
        btn:Click()
        C_Timer.After(0.05, UpdateMinimizeButtonText) -- 状态切换后延迟刷新
    end
end

local whisperButtonFrame, whisperFlashTimer

local function HasUnreadWhispers()
    local db = _G.WChatClassicWhisperDB
    if not (db and db.record and db.record[1]) then return false end
    for _, playerData in pairs(db.record[1]) do
        if playerData and playerData.hasUnread then return true end
    end
    return false
end

local function UpdateWhisperButtonFlash()
    if not whisperButtonFrame then return end
    if whisperFlashTimer then whisperFlashTimer:Cancel(); whisperFlashTimer = nil end
    if HasUnreadWhispers() then
        local isBright = true
        whisperFlashTimer = C_Timer.NewTicker(0.6, function()
            isBright = not isBright
            whisperButtonFrame:SetAlpha(isBright and 1 or 0.1)
        end)
    else
        -- 恢复到用户设置的透明度，而不是强制设置为1
        local config = GetConfig()
        whisperButtonFrame:SetAlpha(config.AlphaOnLeave or 1)
    end
end



local Gold_OnClick = function() if not IsAddOnLoaded("BiaoGe") then LoadAddOn("BiaoGe") end local addon = _G["BG"] if addon and addon.MainFrame then addon.MainFrame:SetShown(not addon.MainFrame:IsVisible()) end end
local AI_OnClick = function() if not IsAddOnLoaded("BiaoGeAI") then LoadAddOn("BiaoGeAI") end local addon = _G["BGAI"] if addon and addon.MainFrame then addon.MainFrame:SetShown(not addon.MainFrame:IsVisible()) end end
local Atlas_OnClick = function() if not IsAddOnLoaded("AtlasLootClassic") then LoadAddOn("AtlasLootClassic") end if SlashCmdList["ATLASLOOT"] then SlashCmdList["ATLASLOOT"]("") end end

-- MeetingHorn按钮点击处理函数
local function MeetingHorn_OnClick()
    if IsAddOnLoaded("MeetingHorn") then
        -- 尝试获取MeetingHorn插件实例
        local meetingHornAddon = LibStub and LibStub('AceAddon-3.0'):GetAddon('MeetingHorn', true)
        if meetingHornAddon and meetingHornAddon.Toggle then
            meetingHornAddon:Toggle()
        else
            -- 备用方法：使用全局绑定
            if _G.BINDING_NAME_MEETINGHORN_TOGGLE and SlashCmdList["MEETINGHORN"] then
                SlashCmdList["MEETINGHORN"]("")
            end
        end
    else
        -- 如果插件未加载，尝试加载
        LoadAddOn("MeetingHorn")
        if IsAddOnLoaded("MeetingHorn") then MeetingHorn_OnClick() end
    end
end

local ChannelButtons = {
    {name = "emote", text = "|TInterface/AddOns/WanTiny/Textures/Emotion/excited.tga:14:14:0:0|t", callback = ClickHandlers.emote},
    {name = "say", text = "说", color = {1.00, 1.00, 1.00}, callback = ClickHandlers.say},
    {name = "yell", text = "喊", color = {1.00, 0.25, 0.25}, callback = ClickHandlers.yell},
    {name = "party", text = "队", color = {0.66, 0.66, 1.00}, callback = ClickHandlers.party},
    {name = "guild", text = "会", color = {0.25, 1.00, 0.25}, callback = ClickHandlers.guild},
    {name = "raid", text = "团", color = {1.00, 0.50, 0.00}, callback = ClickHandlers.raid},
    {name = "bg", text = "战", color = {1.00, 0.50, 0.00}, callback = ClickHandlers.bg},
    {name = "chnGen", text = "综", color = CONFIG.COLORS.CHANNEL_DEFAULT, callback = ClickHandlers.chnGen},
    {name = "chnTrade", text = "交", color = CONFIG.COLORS.CHANNEL_DEFAULT, callback = ClickHandlers.chnTrade},
    {name = "chnLFG", text = "寻", color = CONFIG.COLORS.CHANNEL_DEFAULT, callback = ClickHandlers.chnLFG},
    {name = "world", text = "世", color = {0.78, 1.00, 0.59}, callback = ClickHandlers.world},
    {name = "roll", text = "|TInterface/Buttons/UI-GroupLoot-Dice-Up:16:16:0:0|t", color = {1.00, 1.00, 0.00}, callback = ClickHandlers.roll},
    {name = "chatcopy", text = "复", color = {0.20, 0.60, 0.80}, callback = ClickHandlers.chatcopy},
    {name = "minimize", text = "最", color = {1.00, 0.84, 0.00}, callback = ChatMinimize_OnClick},
    {name = "whisper", text = "|TInterface/ChatFrame/UI-ChatWhisperIcon:16:16:0:0|t", color = {1.00, 0.50, 1.00}, callback = function() if WhisperRecord and WhisperRecord.ToggleWhisperRecordFrame then WhisperRecord.ToggleWhisperRecordFrame() end end}
}

-- 动态添加插件按钮
local addonButtons = {
    {"BiaoGe", "gold", "金", {1.00, 0.84, 0.00}, Gold_OnClick},
    {"BiaoGeAI", "ai", "AI", {0.20, 0.80, 0.80}, AI_OnClick},
    {"AtlasLootClassic", "atlas", "Interface/AddOns/WanTiny/Textures/Icon/Atlas.blp", {0.20, 0.80, 0.20}, Atlas_OnClick},
    {"MeetingHorn", "meetinghorn", "Interface/AddOns/WanTiny/Textures/Icon/Lfg.blp", {0.00, 1.00, 1.00}, MeetingHorn_OnClick}
}
Utils.ProcessChannels(addonButtons, function(btn)
    if IsAddOnLoaded(btn[1]) then
        table.insert(ChannelButtons, #ChannelButtons-2, {name = btn[2], text = btn[3], color = btn[4], callback = btn[5]})
    end
end)

local function CreateChannelButton(data, index, config)
    local frame = CreateFrame("Button", data.name, ChatBar)
    local buttonSize = config.ButtonSize or 22
    frame:SetSize(buttonSize, buttonSize)  -- 使用配置中的按钮大小
    frame:SetAlpha(config.AlphaOnLeave)  -- 设置为默认透明度
    frame:RegisterForClicks("AnyUp")
    frame:SetScript("OnClick", data.callback)
    -- 设置鼠标事件
    local tooltip = TOOLTIPS[data.name]
    if data.name == "meetinghorn" and type(tooltip) == "function" then
        tooltip = tooltip()
    end
    -- 为密语按钮设置特殊的鼠标事件处理
    if data.name == "whisper" then
        frame:SetScript("OnEnter", function(self)
            self:SetAlpha(0.5)
            if tooltip then
                GameTooltip:SetOwner(self, "ANCHOR_RIGHT")
                GameTooltip:SetText(tooltip)
                GameTooltip:Show()
            end
        end)
        frame:SetScript("OnLeave", function(self)
            GameTooltip:Hide()
            -- 如果没有闪烁效果，恢复到配置的透明度
            if not whisperFlashTimer then
                self:SetAlpha(config.AlphaOnLeave)
            end
        end)
    else
        Utils.SetButtonHoverEvents(frame, 0.5, GetConfig().AlphaOnLeave, tooltip)
    end

    -- 创建按钮文字
    frame.text = frame:CreateFontString(nil, "ARTWORK")
    local fontSize = math.max(10, math.min(20, buttonSize * 0.68))
    frame.text:SetFont(STANDARD_TEXT_FONT, fontSize, "OUTLINE")
    frame.text:SetPoint("CENTER", 0, 0)  -- 所有按钮统一居中对齐

    -- 检查是否是需要特殊处理的纹理按钮
    if (data.name == "meetinghorn" or data.name == "atlas") and not Utils.HasMarkup(data.text, "|T") then
        -- 直接使用纹理路径
        local texturePath = data.text
        -- 创建纹理对象
        frame.texture = frame:CreateTexture(nil, "ARTWORK")
        frame.texture:SetTexture(texturePath)
        -- 使用与按钮相同的尺寸，确保图标足够大
        frame.texture:SetSize(buttonSize, buttonSize)
        frame.texture:SetPoint("CENTER", 0, 0)
        -- 根据按钮类型设置颜色
        if data.name == "meetinghorn" then
            frame.texture:SetVertexColor(0.00, 1.00, 1.00)  -- 青色
        elseif data.name == "atlas" then
            frame.texture:SetVertexColor(0.20, 0.80, 0.20)  -- 绿色
        end
        -- 隐藏文本
        frame.text:SetText("")
    else
        frame.text:SetText(data.text)
        -- 只有当color存在且text不包含颜色标记时才设置颜色
        if data.color and not Utils.HasMarkup(data.text, "|c") then
            frame.text:SetTextColor(unpack(data.color))
        end
    end

    -- 创建频道屏蔽X图标
    if CHANNEL_CONFIG.MAPPINGS[data.name] then
        frame.X = frame:CreateTexture(nil, "OVERLAY")
        frame.X:SetTexture("interface/common/voicechat-muted.blp")
        frame.X:SetSize(math.max(12, buttonSize * 0.6), math.max(12, buttonSize * 0.6))
        frame.X:SetAlpha(0.7)
        frame.X:SetPoint("CENTER")
        frame.X:SetDrawLayer("OVERLAY", 7)
        frame.X:Hide()
    end

    frame.buttonName = data.name

    local isVertical = config.UseVertical
    local point, x, y = isVertical and "TOP" or "LEFT",
        isVertical and 0 or (10 + (index - 1) * config.DistanceHorizontal),
        isVertical and ((1 - index) * config.DistanceVertical) or 0
    frame:SetPoint(point, ChatBar, point, x, y)

    if data.name == "bg" then
        BGButtonFrame = frame
        WChat.BGButtonFrame = frame
        UpdateBGButtonText()
    elseif data.name == "minimize" then
        ChatBar.MinimizeBtn = frame
        C_Timer.After(CONFIG.TIME.UI_UPDATE_DELAY, UpdateMinimizeButtonText)
    elseif data.name == "whisper" then
        whisperButtonFrame = frame
        WChat.whisperButtonFrame = frame
    elseif data.name == "emote" then
        WChat.emoteButtonFrame = frame
    end
end

function WChat:InitChatBar()
    local config = GetConfig()
    ChatBar:SetFrameLevel(0)

    -- 清理现有按钮
    Utils.ProcessChannels({ChatBar:GetChildren()}, function(child)
        if child.buttonName then child:Hide(); child:SetParent(nil) end
    end)
    WChat.emoteButtonFrame, BGButtonFrame, WChat.BGButtonFrame, ChatBar.MinimizeBtn = nil, nil, nil, nil

    local buttonSize, padding, isVertical = config.ButtonSize or 22, 8, config.UseVertical
    local width = isVertical and (buttonSize + padding) or (#ChannelButtons * config.DistanceHorizontal + 10)
    local height = isVertical and (#ChannelButtons * config.DistanceVertical + 10) or (buttonSize + padding)
    ChatBar:SetSize(width, height)

    if not _G.ElvUI then
        DEFAULT_CHAT_FRAME.editBox:ClearAllPoints()
        if config.UseTopInput then
            DEFAULT_CHAT_FRAME.editBox:SetPoint("BOTTOMLEFT", DEFAULT_CHAT_FRAME, "TOPLEFT", 0, 20)
            DEFAULT_CHAT_FRAME.editBox:SetPoint("BOTTOMRIGHT", DEFAULT_CHAT_FRAME, "TOPRIGHT", 0, 20)
        else
            DEFAULT_CHAT_FRAME.editBox:SetPoint("TOPLEFT", DEFAULT_CHAT_FRAME, "BOTTOMLEFT", 0, -5)
            DEFAULT_CHAT_FRAME.editBox:SetPoint("TOPRIGHT", DEFAULT_CHAT_FRAME, "BOTTOMRIGHT", 0, -5)
        end
    end

    ChatBar:ClearAllPoints()
    if config.Position and config.Position.point then
        local pos = config.Position
        -- 安全地获取relativeTo对象
        local relativeTo = UIParent  -- 默认值
        if pos.relativeTo and pos.relativeTo ~= "UIParent" then
            relativeTo = _G[pos.relativeTo] or UIParent
        end
        -- 使用正确的坐标字段名
        ChatBar:SetPoint(pos.point, relativeTo, pos.relativePoint, pos.x or 0, pos.y or 90)
    else
        ChatBar:SetPoint("TOPLEFT", DEFAULT_CHAT_FRAME, "TOPLEFT", 0, 90)
    end

    -- 同步 IsMovable 变量与配置状态
    print("=== 聊天条初始化调试 ===")
    print("config.LockChatBar:", config.LockChatBar)
    print("设置前 IsMovable:", IsMovable)
    IsMovable = not config.LockChatBar
    print("设置后 IsMovable:", IsMovable)
    print("调用 SetupChatBarDragging, 参数:", not config.LockChatBar)
    SetupChatBarDragging(ChatBar, not config.LockChatBar)
    print("调用 ChatBarUtils.setBackdrop, 参数:", not config.LockChatBar)
    ChatBarUtils.setBackdrop(not config.LockChatBar)
    print("=== 聊天条初始化调试结束 ===")

    for i = 1, #ChannelButtons do CreateChannelButton(ChannelButtons[i], i, config) end
    -- 移除频道组，但不自动添加任何频道，让用户自己决定
    ChatFrame_RemoveMessageGroup(DEFAULT_CHAT_FRAME, "CHANNEL")

    -- 注册事件
    ChatBar:UnregisterAllEvents()
    Utils.RegisterEvents(ChatBar, {"PLAYER_ENTERING_WORLD", "ZONE_CHANGED_NEW_AREA", "UPDATE_CHAT_WINDOWS", "CHAT_MSG_WHISPER", "CHAT_MSG_BN_WHISPER"})

    ChatBar:SetScript("OnEvent", function(self, event)
        if event == "PLAYER_ENTERING_WORLD" or event == "ZONE_CHANGED_NEW_AREA" then
            UpdateBGButtonText()
            -- 不自动处理频道，完全由用户控制频道的加入和退出
        elseif event == "UPDATE_CHAT_WINDOWS" then
            UpdateChannelXIcons()
        elseif event == "CHAT_MSG_WHISPER" or event == "CHAT_MSG_BN_WHISPER" then
            C_Timer.After(CONFIG.TIME.UI_UPDATE_DELAY, UpdateWhisperButtonFlash)
        end
    end)

    C_Timer.After(1, function() UpdateBGButtonText(); UpdateMinimizeButtonText(); UpdateChannelXIcons(); UpdateWhisperButtonFlash() end)
    if not WChat.chatBarInitialized then WChat.chatBarInitialized = true end
end

-- 频道快速切换 - 安全的全局函数重写
local originalChatEdit_CustomTabPressed = ChatEdit_CustomTabPressed
function ChatEdit_CustomTabPressed(...)
    local success, result = pcall(ChatEdit_CustomTabPressed_Inner, ...)
    if success then
        return result
    else
        -- 如果自定义函数失败，回退到原始函数
        if originalChatEdit_CustomTabPressed then
            return originalChatEdit_CustomTabPressed(...)
        end
        return nil
    end
end

local cycles = {
        -- "说"
        {
            chatType = "SAY",
            use = function(self, editbox)
                return 1
            end
        },
        --大喊
        {
            chatType = "YELL",
            use = function(self, editbox)
                return 1
            end
        },
        --小队
        {
            chatType = "PARTY",
            use = function(self, editbox)
                return IsInGroup()
            end
        },
        --团队
        {
            chatType = "RAID",
            use = function(self, editbox)
                return IsInRaid()
            end
        },
        --实时聊天
        {
            chatType = "INSTANCE_CHAT",
            use = function(self, editbox)
                return select(2, IsInInstance()) == "pvp"
            end
        },
        --公会
        {
            chatType = "GUILD",
            use = function(self, editbox)
                return IsInGuild()
            end
        },
        --频道
        {
            chatType = "CHANNEL",
            use = function(self, editbox, currChatType)
                local currNum
                if currChatType ~= "CHANNEL" then
                    currNum = IsShiftKeyDown() and 21 or 0
                else
                    currNum = editbox:GetAttribute("channelTarget")
                end
                local h, r, step = currNum + 1, 20, 1
                if IsShiftKeyDown() then
                    h, r, step = currNum - 1, 1, -1
                end
                for i = h, r, step do
                    local channelNum, channelName = GetChannelName(i)
                    if channelNum and channelNum > 0 and channelName and channelName:find("大脚世界频道") then
                        editbox:SetAttribute("channelTarget", i)
                        return true
                    end
                end
            end
        },
        {
            chatType = "SAY",
            use = function(self, editbox)
                return 1
            end
        }
}

local chatTypeBeforeSwitch, tellTargetBeforeSwitch --记录在频道和密语之间切换时的状态
function ChatEdit_CustomTabPressed_Inner(self)
    if strsub(tostring(self:GetText()), 1, 1) == "/" then
        return
    end
    local currChatType = self:GetAttribute("chatType")
    if (IsControlKeyDown()) then
        if (currChatType == "WHISPER" or currChatType == "BN_WHISPER") then
            --记录之前的密语对象，以便后续切回
            self:SetAttribute("chatType", chatTypeBeforeSwitch or "SAY")
            ChatEdit_UpdateHeader(self)
            chatTypeBeforeSwitch = "WHISPER"
            tellTargetBeforeSwitch = self:GetAttribute("tellTarget")
            return --这里和下面不同，这里可以不返回true
        else
            local newTarget, newTargetType = ChatEdit_GetNextTellTarget()
            if tellTargetBeforeSwitch or (newTarget and newTarget ~= "") then
                self:SetAttribute("chatType", tellTargetBeforeSwitch and chatTypeBeforeSwitch or newTargetType)
                self:SetAttribute("tellTarget", tellTargetBeforeSwitch or newTarget)
                ChatEdit_UpdateHeader(self)
                chatTypeBeforeSwitch = currChatType
                tellTargetBeforeSwitch = nil
                return true --这里必须返回true，否则会被暴雪默认的再切换一次密语对象
            end
        end
    end

    --对于说然后SHIFT的情况，因为没有return，所以第一层循环会一直遍历到最后的SAY
    for i, curr in ipairs(cycles) do
        if curr.chatType == currChatType then
            local h, r, step = i + 1, #cycles, 1
            if IsShiftKeyDown() then
                h, r, step = i - 1, 1, -1
            end
            if currChatType == "CHANNEL" then
                h = i
            end
            for j = h, r, step do
                if cycles[j]:use(self, currChatType) then
                    self:SetAttribute("chatType", cycles[j].chatType)
                    ChatEdit_UpdateHeader(self)
                    return
                end
            end
        end
    end
end

-- 聊天表情
local EmoteTableFrame
local fmtstring
local customEmoteStartIndex = 9
local function CreateEmotes()
    local emotes = {}
    for i = 1, 8 do
        emotes[i] = {"{rt" .. i .. "}", "Interface\\TargetingFrame\\UI-RaidTargetingIcon_" .. i}
    end

    local emoteMap = {
        "天使,Angel", "生气,Angry", "大笑,Biglaugh", "鼓掌,Clap", "酷,Cool", "哭,Cry", "可爱,Cutie", "鄙视,Despise", "美梦,Dreamsmile", "尴尬,Embarrass", "邪恶,Evil", "兴奋,Excited", "晕,Faint", "打架,Fight", "流感,Flu", "呆,Freeze", "皱眉,Frown", "致敬,Greet", "鬼脸,Grimace", "龇牙,Growl", "开心,Happy", "心,Heart", "恐惧,Horror", "生病,Ill", "无辜,Innocent", "功夫,Kongfu", "花痴,Love", "邮件,Mail", "化妆,Makeup", "沉思,Meditate", "可怜,Miserable", "好,Okay", "漂亮,Pretty", "吐,Puke", "握手,Shake", "喊,Shout", "闭嘴,Shuuuu", "害羞,Shy", "睡觉,Sleep", "微笑,Smile", "吃惊,Suprise", "失败,Surrender", "流汗,Sweat", "流泪,Tear", "悲剧,Tears", "想,Think", "偷笑,Titter", "猥琐,Ugly", "胜利,Victory", "雷锋,Volunteer", "委屈,Wronged"
    }
    for i, pair in ipairs(emoteMap) do
        local cn, en = pair:match("([^,]+),([^,]+)")
        emotes[8 + i] = {"{" .. cn .. "}", PATHS.EMOTION .. "\\" .. en}
    end
    return emotes
end
local emotes = CreateEmotes()

local function ChatEmoteFilter(self, event, msg, ...)
    local config = GetConfig()
    if config.EnableEmoteInput then
        for i = customEmoteStartIndex, #emotes do
            if msg:find(emotes[i][1]) then
                msg = msg:gsub(emotes[i][1], format(fmtstring, emotes[i][2]), 1)
            end
        end
    end
    return false, msg, ...
end

local function EmoteIconMouseUp(frame, button)
    if (button == "LeftButton") then
        local chatFrame = GetCVar("chatStyle")=="im" and SELECTED_CHAT_FRAME or DEFAULT_CHAT_FRAME
        local eb = chatFrame and chatFrame.editBox
        if(eb) then
            eb:Insert(frame.text)
            eb:Show();
            eb:SetFocus()
        end
    end
    WChat:ToggleEmoteTable()
end

function WChat:InitEmoteTableFrame()
    local config = GetConfig()
    local chatFontSize = floor(select(2, SELECTED_CHAT_FRAME:GetFont()))
    fmtstring = format("\124T%%s:%d\124t", chatFontSize)

    -- 清理旧的表情框架
    if EmoteTableFrame then
        EmoteTableFrame:Hide()
        if EmoteTableFrame.clickCatcher then
            EmoteTableFrame.clickCatcher:Hide()
            EmoteTableFrame.clickCatcher:SetParent(nil)
            EmoteTableFrame.clickCatcher = nil
        end
        EmoteTableFrame:SetParent(nil)
        EmoteTableFrame = nil
    end

    EmoteTableFrame = CreateFrame("Frame", "EmoteTableFrame", UIParent, "BackdropTemplate")

    EmoteTableFrame:SetMovable(true)
    EmoteTableFrame:RegisterForDrag("LeftButton")
    EmoteTableFrame:SetScript("OnDragStart", EmoteTableFrame.StartMoving)
    EmoteTableFrame:SetScript("OnDragStop", EmoteTableFrame.StopMovingOrSizing)
    EmoteTableFrame:EnableMouse(true)
    Utils.SetStandardBackdrop(EmoteTableFrame, 0.8, 1, "emote")
    EmoteTableFrame:SetBackdropColor(0.05, 0.05, 0.05, 0.8)
    EmoteTableFrame:SetBackdropBorderColor(0.3, 0.3, 0.3)
    EmoteTableFrame:SetWidth((config.EmoteIconListSize + 6) * 12 + 10)
    EmoteTableFrame:SetHeight((config.EmoteIconListSize + 6) * 5 + 10)

    local function setupClickCatcher(self)
        if not self.clickCatcher then
            self.clickCatcher = CreateFrame("Frame", nil, UIParent)
            self.clickCatcher:SetAllPoints(UIParent)
            self.clickCatcher:SetFrameLevel(self:GetFrameLevel() - 1)
            self.clickCatcher:EnableMouse(true)
            self.clickCatcher:SetScript("OnMouseDown", function()
                if EmoteTableFrame and EmoteTableFrame:IsShown() then EmoteTableFrame:Hide() end
            end)
        end
        self.clickCatcher:Show()
    end

    if WChat.emoteButtonFrame then
        EmoteTableFrame:SetPoint("BOTTOM", WChat.emoteButtonFrame, "TOP", 0, 5)
        EmoteTableFrame:SetScript("OnShow", function(self)
            local panelWidth, screenWidth, left, right = self:GetWidth(), UIParent:GetWidth(), self:GetLeft(), self:GetRight()
            if left and right then
                local offsetX = 0
                if left < 0 then offsetX = -left + 10
                elseif right > screenWidth then offsetX = screenWidth - right - 10 end
                if offsetX ~= 0 then
                    self:ClearAllPoints()
                    self:SetPoint("BOTTOM", WChat.emoteButtonFrame, "TOP", offsetX, 5)
                end
            end
            setupClickCatcher(self)
        end)
    else
        EmoteTableFrame:SetPoint("BOTTOM", ChatFrame1EditBox, "TOP", 0, 30)
        EmoteTableFrame:SetScript("OnShow", setupClickCatcher)
    end
    EmoteTableFrame:SetScript("OnHide", function(self) if self.clickCatcher then self.clickCatcher:Hide() end end)

    EmoteTableFrame:Hide()
    EmoteTableFrame:SetFrameStrata("DIALOG")

    local icon, row, col
    row = 1
    col = 1
    for i = 1, #emotes do
        text = emotes[i][1]
        texture = emotes[i][2]
        icon = CreateFrame("Frame", format("IconButton%d", i), EmoteTableFrame)
        icon:SetWidth(config.EmoteIconListSize + 6)
        icon:SetHeight(config.EmoteIconListSize + 6)
        icon.text = text
        icon.texture = icon:CreateTexture(nil, "ARTWORK")
        icon.texture:SetTexture(texture)
        icon.texture:SetAllPoints(icon)
        icon:Show()
        icon:SetPoint(
            "TOPLEFT",
            5 + (col - 1) * (config.EmoteIconListSize + 6),
            -5 - (row - 1) * (config.EmoteIconListSize + 6)
        )
        icon:SetScript("OnMouseUp", EmoteIconMouseUp)
        icon:EnableMouse(true)
        col = col + 1
        if col > 12 then row = row + 1; col = 1 end
    end

    -- 清理旧的表情事件过滤器（如果存在）
    if self.emoteEventsRegistered then
        local emoteEvents = {
            "CHAT_MSG_CHANNEL", "CHAT_MSG_SAY", "CHAT_MSG_YELL", "CHAT_MSG_RAID", "CHAT_MSG_RAID_LEADER",
            "CHAT_MSG_PARTY", "CHAT_MSG_PARTY_LEADER", "CHAT_MSG_GUILD", "CHAT_MSG_AFK", "CHAT_MSG_DND",
            "CHAT_MSG_INSTANCE_CHAT", "CHAT_MSG_INSTANCE_CHAT_LEADER", "CHAT_MSG_WHISPER", "CHAT_MSG_WHISPER_INFORM",
            "CHAT_MSG_BN_WHISPER", "CHAT_MSG_BN_WHISPER_INFORM", "CHAT_MSG_COMMUNITIES_CHANNEL"
        }
        for _, event in ipairs(emoteEvents) do
            if self.registeredFilters[event] then
                for _, callback in ipairs(self.registeredFilters[event]) do
                    if callback == ChatEmoteFilter then
                        ChatFrame_RemoveMessageEventFilter(event, callback)
                    end
                end
            end
        end
    end
    
    -- 注册表情事件过滤器
    Utils.ProcessChannels({
        "CHAT_MSG_CHANNEL", "CHAT_MSG_SAY", "CHAT_MSG_YELL", "CHAT_MSG_RAID", "CHAT_MSG_RAID_LEADER",
        "CHAT_MSG_PARTY", "CHAT_MSG_PARTY_LEADER", "CHAT_MSG_GUILD", "CHAT_MSG_AFK", "CHAT_MSG_DND",
        "CHAT_MSG_INSTANCE_CHAT", "CHAT_MSG_INSTANCE_CHAT_LEADER", "CHAT_MSG_WHISPER", "CHAT_MSG_WHISPER_INFORM",
        "CHAT_MSG_BN_WHISPER", "CHAT_MSG_BN_WHISPER_INFORM", "CHAT_MSG_COMMUNITIES_CHANNEL"
    }, function(event) self:RegisterFilter(event, ChatEmoteFilter) end)
    self.emoteEventsRegistered = true
end

function WChat:ToggleEmoteTable()
    if not EmoteTableFrame then return end
    if EmoteTableFrame:IsShown() then EmoteTableFrame:Hide() else EmoteTableFrame:Show() end
end

-- 聊天美化（基于源代码完整实现）
local function SkinChatTabs()
    if _G.ElvUI then
        if not WChat.elvuiWarningShown then WChat.elvuiWarningShown = true end
        return
    end

    local inherit = GameFontNormalSmall

    -- 直接获取职业颜色，避免调用可能导致递归的GetClassRGB函数
    local _, playerClass = UnitClass("player")
    local r, g, b = 1, 0.82, 0  -- 默认金色
    if RAID_CLASS_COLORS and RAID_CLASS_COLORS[playerClass] then
        local classColor = RAID_CLASS_COLORS[playerClass]
        r, g, b = classColor.r, classColor.g, classColor.b
    end
    local color = {r = r, g = g, b = b}

    local function updateFS(tab, inc, flags, ...)
        local fstring = tab:GetFontString()
        if not fstring then return end
        local font, fontSize = inherit:GetFont()
        if inc then
            fstring:SetFont(font, fontSize + 1, flags)
        else
            fstring:SetFont(font, fontSize, flags)
        end
        if select('#', ...) > 0 then
            fstring:SetTextColor(...)
        end
    end

    local function OnEnter(self)
        updateFS(self, nil, "OUTLINE", color.r, color.g, color.b)
    end

    local function OnLeave(self)
        local r, g, b
        local id = self:GetID()
        local emphasis = _G["ChatFrame"..id..'TabFlash']:IsShown()
        if (_G["ChatFrame"..id] == SELECTED_CHAT_FRAME) then
            r, g, b = color.r, color.g, color.b
        elseif emphasis then
            r, g, b = 1, 1, 1
        else
            r, g, b = 1, 1, 1
        end
        updateFS(self, emphasis, nil, r, g, b)
    end

    local function faneifyTab(tab)
        if not tab then return end

        if not tab.Fane then
            if tab.leftTexture then tab.leftTexture:Hide() end
            if tab.middleTexture then tab.middleTexture:Hide() end
            if tab.rightTexture then tab.rightTexture:Hide() end
            if tab.leftSelectedTexture then
                tab.leftSelectedTexture:Hide()
                -- 安全的方法重写：保存原始方法并创建安全的替代
                if not tab.leftSelectedTexture._originalShow then
                    tab.leftSelectedTexture._originalShow = tab.leftSelectedTexture.Show
                end
                tab.leftSelectedTexture.Show = function() end  -- 空函数而不是Hide
            end
            if tab.middleSelectedTexture then
                tab.middleSelectedTexture:Hide()
                if not tab.middleSelectedTexture._originalShow then
                    tab.middleSelectedTexture._originalShow = tab.middleSelectedTexture.Show
                end
                tab.middleSelectedTexture.Show = function() end
            end
            if tab.rightSelectedTexture then
                tab.rightSelectedTexture:Hide()
                if not tab.rightSelectedTexture._originalShow then
                    tab.rightSelectedTexture._originalShow = tab.rightSelectedTexture.Show
                end
                tab.rightSelectedTexture.Show = function() end
            end
            if tab.leftHighlightTexture then tab.leftHighlightTexture:Hide() end
            if tab.middleHighlightTexture then tab.middleHighlightTexture:Hide() end
            if tab.rightHighlightTexture then tab.rightHighlightTexture:Hide() end
            tab:HookScript('OnEnter', OnEnter)
            tab:HookScript('OnLeave', OnLeave)
            tab:SetAlpha(1)
            tab.Fane = true
        end

        local id = tab:GetID()
        if id == SELECTED_CHAT_FRAME:GetID() then
            updateFS(tab, nil, nil, color.r, color.g, color.b)
        else
            updateFS(tab, nil, nil, 1, 1, 1)
        end
    end

    hooksecurefunc('FCFTab_UpdateColors', faneifyTab)
    local tabCount = 0
    for i=1, NUM_CHAT_WINDOWS do
        local tab = _G['ChatFrame'..i..'Tab']
        if tab then faneifyTab(tab); tabCount = tabCount + 1 end
    end

end

local function CreateChatMinimizeButton()
    if _G.WChat_ChatHider then return end
    local ChatHider = CreateFrame("Frame", "WChat_ChatHider", UIParent)
    ChatHider:SetSize(1,1)
    ChatHider:SetFrameStrata("LOW")
    ChatHider:SetPoint("BOTTOMLEFT", UIParent, 0, 0)
    local btn = CreateFrame("Button", "WChat_ChatHideButton", UIParent)
    btn:SetSize(32,36)
    btn:Hide()
    local ChatIsHidden = false

    btn:SetScript("OnClick", function(self, button)
        if not ChatIsHidden then
            Utils.ProcessChatFrames(function(chatFrame, i) chatFrame:SetParent(ChatHider) end)
            if _G.GeneralDockManager then _G.GeneralDockManager:SetParent(ChatHider) end
            if _G.ChatFrameMenuButton then _G.ChatFrameMenuButton:SetParent(ChatHider) end
            if _G.ChatFrameChannelButton then _G.ChatFrameChannelButton:SetParent(ChatHider) end
            ChatHider:Hide()
            ChatIsHidden = true
        else
            ChatHider:Show()
            ChatIsHidden = false
        end
    end)
end

function WChat:InitChatSkin()
    if _G.ElvUI then
        return
    end

    SkinChatTabs()
    CreateChatMinimizeButton()
end

--聊天复制
local chatCopyFrame = CreateFrame("Frame", "ChatCopyFrame", UIParent, "BackdropTemplate")
chatCopyFrame:SetPoint("CENTER", UIParent, "CENTER")
chatCopyFrame:SetSize(700, 400)
chatCopyFrame:Hide()
chatCopyFrame:SetFrameStrata("DIALOG")
chatCopyFrame:SetMovable(true)
chatCopyFrame:EnableMouse(true)
chatCopyFrame:RegisterForDrag("LeftButton")
chatCopyFrame:SetScript("OnDragStart", chatCopyFrame.StartMoving)
chatCopyFrame:SetScript("OnDragStop", chatCopyFrame.StopMovingOrSizing)
chatCopyFrame.close = CreateFrame("Button", nil, chatCopyFrame, "UIPanelCloseButton")
chatCopyFrame.close:SetPoint("TOPRIGHT", chatCopyFrame, "TOPRIGHT")
chatCopyFrame:SetBackdrop({
    bgFile = "Interface/DialogFrame/UI-DialogBox-Background",
    edgeFile = "Interface/DialogFrame/UI-DialogBox-Border",
    tile = true,
    tileSize = 16,
    edgeSize = 16,
    insets = {left = 4, right = 4, top = 4, bottom = 4}
})

local scrollArea = CreateFrame("ScrollFrame", "ChatCopyScrollFrame", chatCopyFrame, "UIPanelScrollFrameTemplate")
scrollArea:SetPoint("TOPLEFT", chatCopyFrame, "TOPLEFT", 10, -30)
scrollArea:SetPoint("BOTTOMRIGHT", chatCopyFrame, "BOTTOMRIGHT", -30, 10)
local editBox = CreateFrame("EditBox", nil, chatCopyFrame)
editBox:SetMultiLine(true)
editBox:SetMaxLetters(99999)
editBox:EnableMouse(true)
editBox:SetAutoFocus(false)
editBox:SetFontObject(ChatFontNormal)
editBox:SetWidth(scrollArea:GetWidth())
editBox:SetHeight(270)
editBox:SetScript("OnEscapePressed", function(f)f:GetParent():GetParent():Hide()f:SetText("") end)
scrollArea:SetScrollChild(editBox)

-- 去除富文本标记按钮
local stripBtn = CreateFrame("Button", nil, chatCopyFrame, "UIPanelButtonTemplate")
stripBtn:SetSize(80, 22)
stripBtn:SetText("纯文本")
stripBtn:SetPoint("BOTTOMRIGHT", chatCopyFrame, "BOTTOMRIGHT", -10, 10)
stripBtn:SetScript("OnClick", function()
    local str = editBox:GetText()
    -- 去除图片、超链接、颜色等富文本标记
    str = str:gsub("|A.-|a", "") -- 图片
    str = str:gsub("|T.-|t", "") -- 图片
    str = str:gsub("|H.-|h(.-)|h", "%1") -- 超链接只保留显示文本
    str = str:gsub("|c%x%x%x%x%x%x%x%x", "") -- 颜色开始
    str = str:gsub("|r", "") -- 颜色结束
    editBox:SetText(str)
end)

function WChat:CopyFunc()
    local cf = SELECTED_CHAT_FRAME
    local lines = {}
    if not cf then
        return
    end
    local numMessages = cf:GetNumMessages()
    if numMessages == 0 then
        return
    end
    
    for i = 1, numMessages do
        local msg = cf:GetMessageInfo(i)
        if msg then
            -- 只清理部分富文本标记，保留颜色和基本格式
            msg = msg:gsub("|H.-|h(.-)|h", "%1") -- 保留超链接显示文本
            msg = msg:gsub("|H.-|h", "") -- 移除剩余的超链接开始标记
            msg = msg:gsub("|h", "") -- 移除超链接结束标记
            msg = msg:gsub("|T.-|t", "[表情]") -- 替换表情为文本
            msg = msg:gsub("|A.-|a", "") -- 移除其他图片标记
            -- 保留颜色标记 |c 和 |r
            table.insert(lines, msg)
        end
    end
    
    local text = table.concat(lines, "\n")
    chatCopyFrame:Show()
    editBox:SetText(text)
    editBox:HighlightText(0)
end

function WChat:CopySingleMessage(messageId)
    if not WChat.messageCache or not WChat.messageCache[messageId] then return end
    local originalMessage = WChat.messageCache[messageId]
    local cleanMessage = originalMessage
        :gsub("|c%x%x%x%x%x%x%x%x", ""):gsub("|r", "")
        :gsub("|H.-|h", ""):gsub("|h", "")
        :gsub("|T.-|t", "[表情]")
    chatCopyFrame:Show()
    editBox:SetText(cleanMessage)
    editBox:HighlightText(0)
end



local dbCache = {
    globalDB = nil,
    lastAccess = 0,
    cacheTimeout = 1
}

local function GetWhisperGlobalDB()
    local currentTime = GetTime()

    if dbCache.globalDB and (currentTime - dbCache.lastAccess) < dbCache.cacheTimeout then return dbCache.globalDB end
    if not WChatClassicWhisperDB then
        WChatClassicWhisperDB = {
            ["Open"] = true,
            ["Tips"] = true,
            ["SoundAlert"] = true,
            ["Days"] = 30,
            ["record"] = {{}, {}}
        }
    end
    if WChatClassicWhisperDB.SoundAlert == nil then WChatClassicWhisperDB.SoundAlert = true end
    dbCache.globalDB = WChatClassicWhisperDB
    dbCache.lastAccess = currentTime

    return WChatClassicWhisperDB
end



-- 统一的玩家数据获取（减少重复的安全检查）
local function GetPlayerData(playerName)
    local globalDB = GetWhisperGlobalDB()
    return globalDB and globalDB.record and globalDB.record[1] and globalDB.record[1][playerName]
end

-- 统一的数据库操作后处理
local function PostDBOperation(needsCacheClean, needsUIUpdate)
    if needsCacheClean then C_Timer.After(CONFIG.TIME.UI_UPDATE_DELAY, ClearDBCache) end
    if needsUIUpdate then C_Timer.After(CONFIG.TIME.UI_UPDATE_DELAY, UpdateWhisperButtonFlash) end
end





-- WanTinyUI风格工具函数（简化版）
local function GetClassRGB()
    local _, playerClass = UnitClass("player")

    local r, g, b = 1, 0.82, 0  -- 默认金色
    if RAID_CLASS_COLORS and RAID_CLASS_COLORS[playerClass] then
        local classColor = RAID_CLASS_COLORS[playerClass]
        r, g, b = classColor.r, classColor.g, classColor.b
    end

    return r, g, b
end

local function SetWanTinyFont(fontString, size)
    fontString:SetFont(STANDARD_TEXT_FONT, size or 14, "OUTLINE")
end

local function SetWanTinyBackdrop(frame, bgAlpha, borderAlpha)
    if not frame.SetBackdrop then
        Mixin(frame, BackdropTemplateMixin)
        frame:OnBackdropLoaded()
    end
    local r, g, b = GetClassRGB()
    frame:SetBackdrop({
        bgFile = "Interface/ChatFrame/ChatFrameBackground",
        edgeFile = "Interface/ChatFrame/ChatFrameBackground",
        edgeSize = 1
    })
    frame:SetBackdropColor(0, 0, 0, bgAlpha or 0.1)
    frame:SetBackdropBorderColor(r, g, b, borderAlpha or 0.5)
end

-- 删除WhisperUtils重复定义，直接使用Utils
-- 扩展Utils功能
Utils.CLASS_COLORS = CLASS_COLORS
Utils.CLASS_ICON_TCOORDS = CLASS_ICON_COORDS
Utils.GetClassRGB = GetClassRGB
Utils.SetWanTinyFont = SetWanTinyFont
Utils.SetWanTinyBackdrop = SetWanTinyBackdrop

function Utils.CreateTitle(parent, text, font, yOffset)
    local title = parent:CreateFontString(nil, "OVERLAY", font or "GameFontNormal")
    title:SetPoint("TOP", parent, "TOP", 0, yOffset or -4)
    title:SetText(text or "")
    title:SetTextColor(CONFIG.COLORS.TITLE.r, CONFIG.COLORS.TITLE.g, CONFIG.COLORS.TITLE.b, CONFIG.COLORS.TITLE.a)
    SetWanTinyFont(title, 16)
    return title
end

function Utils.CreateCloseButton(parent, onClick)
    local btn = CreateFrame("Button", nil, parent, "UIPanelCloseButton")
    btn:SetPoint("TOPRIGHT", parent, "TOPRIGHT", -2, -2)
    btn:SetSize(24, 24)
    if onClick then btn:SetScript("OnClick", onClick) end
    return btn
end

function Utils.CreateMessageFrame(parent, width, height)
    local msgFrame = CreateFrame("ScrollingMessageFrame", nil, parent)
    msgFrame:SetSize(width or 280, height or 150)
    msgFrame:SetFont(STANDARD_TEXT_FONT, 13, "OUTLINE")
    msgFrame:SetJustifyH("LEFT")
    msgFrame:SetMaxLines(CONFIG.UI.MAX_LINES)
    msgFrame:SetFading(false)
    msgFrame:SetHyperlinksEnabled(true)
    msgFrame:EnableMouseWheel(true)
    msgFrame:SetScript("OnMouseWheel", function(self, delta)
        if delta > 0 then self:ScrollUp() else self:ScrollDown() end
    end)
    return msgFrame
end
function Utils.AddFormattedMessage(messageFrame, msgData, opts)
    local timeFmt = (opts and opts.history) and "%H:%M:%S" or (opts and opts.timeFmt or "%m-%d %H:%M")
    local timeStr = date(timeFmt, msgData.time)
    local prefix = msgData.incoming and "[收到]" or "[发送]"
    local color = msgData.incoming and "|cff00ff00" or "|cff00bfff"
    if msgData.isFromBNet then prefix = prefix .. "(战网)" end

    local playerName = msgData.playerName or msgData.sender or msgData.target or ""
    -- 优先使用消息数据中的玩家名称，避免数据混乱
    if not playerName or playerName == "" then
        if msgData.playerName then
            playerName = msgData.playerName
        else
            local parent = messageFrame and messageFrame:GetParent()
            if parent and parent.currentPlayer then playerName = parent.currentPlayer end
        end
    end

    local displayName = Utils.GetDisplayName(playerName)
    local className = msgData.class
    -- 优先使用消息数据中的玩家名称获取职业信息
    local targetPlayerName = msgData.playerName or playerName
    if targetPlayerName then
        local playerData = GetPlayerData(targetPlayerName)
        if playerData and playerData.class then className = playerData.class end
    end

    local classColorCode = Utils.GetClassColorCode(className)
    local fullMessage = string.format("%s[%s]%s[%s%s|r]|r：%s%s|r",
        color, timeStr, prefix, classColorCode, displayName or "", "|cffff80ff", msgData.message)
    messageFrame:AddMessage(fullMessage)
end

function Utils.SetFrameBackdrop(frame, bgAlpha)
    SetWanTinyBackdrop(frame, bgAlpha, 0.5)
end

function Utils.CreateMovableFrame(name, parent, width, height)
    local frame = CreateFrame("Frame", name, parent, "BackdropTemplate")
    frame:SetSize(width, height)
    frame:SetMovable(true)
    frame:EnableMouse(true)
    frame:RegisterForDrag("LeftButton")
    frame:SetScript("OnDragStart", frame.StartMoving)
    frame:SetScript("OnDragStop", frame.StopMovingOrSizing)
    return frame
end
function Utils.CreateButton(parent, text, width, height)
    local r, g, b = GetClassRGB()
    local button = CreateFrame("Button", nil, parent, "BackdropTemplate")
    button:SetSize(width or 80, height or 22)
    SetWanTinyBackdrop(button, 0.07, 0.5)

    local fontString = button:CreateFontString(nil, "OVERLAY")
    fontString:SetAllPoints()
    SetWanTinyFont(fontString, 14)
    fontString:SetText(text or "")
    fontString:SetTextColor(1, 1, 1, 1)

    -- 使用统一的按钮悬停效果
    button:SetScript("OnEnter", function(self)
        self:SetBackdropColor(r, g, b, 0.28)
        self:SetBackdropBorderColor(r, g, b, 1)
    end)
    button:SetScript("OnLeave", function(self)
        self:SetBackdropColor(0, 0, 0, 0.07)
        self:SetBackdropBorderColor(r, g, b, 0.5)
    end)
    return button
end

function Utils.CreateCheckbox(parent, text, dbKey)
    local checkbox = CreateFrame("CheckButton", nil, parent, "UICheckButtonTemplate")
    checkbox:SetSize(20, 20)
    checkbox.text = checkbox:CreateFontString(nil, "OVERLAY", "GameFontNormal")
    checkbox.text:SetPoint("LEFT", checkbox, "RIGHT", 5, 0)
    checkbox.text:SetText(text or "")
    if dbKey then
        local db = GetWhisperGlobalDB()
        checkbox:SetChecked(db[dbKey])
        checkbox:SetScript("OnClick", function(self) db[dbKey] = self:GetChecked() end)
    end
    return checkbox
end

-- 设置WhisperRecord.Utils
WhisperRecord.Utils = Utils

local function SetAutoHideTimer(chatContent, delay)
    if chatContent.hideTimer then chatContent.hideTimer:Cancel() end
    chatContent.hideTimer = C_Timer.NewTimer(delay or CONFIG.TIME.AUTO_HIDE_DELAY, function()
        if chatContent:IsShown() then
            chatContent:Hide()
            WhisperRecord.ClearSelectedState()
        end
    end)
end



WhisperRecord.Utils.GetPlayerClassColor = function(class)
    return WhisperRecord.Utils.CLASS_COLORS[class] or WhisperRecord.Utils.CLASS_COLORS["PRIEST"]
end

WhisperRecord.Utils.GetClassIcon = function(class)
    if class == "BN_2" then
        return "interface/friendsframe/battlenet-portrait.blp", {0, 1, 0, 1}
    elseif class and WhisperRecord.Utils.CLASS_ICON_TCOORDS[class] then
        return "Interface/TargetingFrame/UI-Classes-Circles", WhisperRecord.Utils.CLASS_ICON_TCOORDS[class]
    else
        return nil, nil
    end
end

local ClassInfo = {
    playerInfoCache = {},
    queue = {},
    Cache = function(self, playerName, class) if playerName and class then self.playerInfoCache[playerName] = class end end,
    Get = function(self, playerName)
        if not playerName then return nil end
        local cached = self.playerInfoCache[playerName]
        if cached then return cached end
        for _, unit in ipairs({"target", "mouseover"}) do
            local _, class = UnitClass(unit)
            if class and UnitName(unit) == playerName then
                self:Cache(playerName, class)
                return class
            end
        end
        return nil
    end,
    AddRetry = function(self, playerName, callback)
        if not callback then return end
        for _, item in ipairs(self.queue) do if item.playerName == playerName then return end end
        table.insert(self.queue, {playerName = playerName, callback = callback, retries = 0})
    end,
    Process = function(self)
        if #self.queue == 0 then return end
        local item = table.remove(self.queue, 1)
        if item.retries < 3 then
            local class = self:Get(item.playerName)
            if class then
                item.callback(class)
            else
                item.retries = item.retries + 1
                table.insert(self.queue, item)
            end
        end
    end
}
WhisperRecord.Utils.ClassInfo = ClassInfo

-- 获取玩家职业信息
local function GetPlayerClassInfo(playerName, guid, isFromBNet, bnSenderID)
    local playerClass, finalPlayerName = nil, playerName

    if isFromBNet then
        playerClass = CONFIG.BNET_CLASS_ID

        if bnSenderID and type(bnSenderID) == "number" and bnSenderID > 0 then
            local success, accountInfo = pcall(C_BattleNet.GetAccountInfoByID, bnSenderID)
            if success and accountInfo then
                finalPlayerName = accountInfo.accountName or playerName
            end
        end
    else
        -- 普通玩家处理
        if guid and string.find(guid, "Player-") then
            local _, class = GetPlayerInfoByGUID(guid)
            if class then
                playerClass = class
            end
        end
        local _, realm = strsplit("-", playerName)
        if not realm then
            finalPlayerName = playerName .. "-" .. GetRealmName()
        else
            finalPlayerName = playerName
        end

        if playerClass then
            WhisperRecord.Utils.ClassInfo:Cache(finalPlayerName, playerClass)
        else
            playerClass = WhisperRecord.Utils.ClassInfo:Get(finalPlayerName)
            if not playerClass then
                WhisperRecord.Utils.ClassInfo:AddRetry(finalPlayerName, function(name, class)
                    WhisperRecord.UpdatePlayerClass(name, class)
                end)
            end
        end
    end

    return finalPlayerName, playerClass
end

local function RecordWhisperInternal(playerName, message, isIncoming, isFromBNet, playerClass, showTip)
    if not GetWhisperGlobalDB().Open then return end

    local finalPlayerName = WhisperRecord.SaveMessage(playerName, {
        time = time(),
        message = message,
        incoming = isIncoming,
        isFromBNet = isFromBNet,
        class = playerClass
    }, playerClass, isIncoming)

    -- UI更新由按钮闪烁通知处理，避免重复更新

    if isIncoming and GetWhisperGlobalDB().SoundAlert then
        PlaySoundFile(PATHS.TEXTURE .. "\\Sounds\\Notify.ogg", "Master")
    end
end

function WhisperRecord.RecordWhisperMessage(event, ...)
    local message, sender, guid, bnSenderID = select(1, ...), select(2, ...), select(12, ...), select(13, ...)
    if not message or not sender then return end
    local isFromBNet = (event == "CHAT_MSG_BN_WHISPER")
    local playerName, playerClass = GetPlayerClassInfo(sender, guid, isFromBNet, bnSenderID)
    RecordWhisperInternal(playerName, message, true, isFromBNet, playerClass, true)
end

function WhisperRecord.RecordSentWhisper(message, target, guid, isBNet)
    if not message or not target then return end
    local playerName, playerClass = GetPlayerClassInfo(target, guid, isBNet or false, nil)
    RecordWhisperInternal(playerName, message, false, isBNet or false, playerClass, false)
end

function WhisperRecord.SaveMessage(playerName, messageData, playerClass, hasUnread)
    if hasUnread == nil then hasUnread = messageData.incoming end
    local dateKey = date("%Y%m%d", messageData.time)
    local db = GetWhisperGlobalDB()
    if not db.record[1][playerName] then
        db.record[1][playerName] = {class = playerClass, lastTime = messageData.time, messages = {}, hasUnread = hasUnread}
    end
    if not db.record[1][playerName].messages[dateKey] then
        db.record[1][playerName].messages[dateKey] = {}
    end
    table.insert(db.record[1][playerName].messages[dateKey], messageData)
    db.record[1][playerName].lastTime = messageData.time
    db.record[1][playerName].class = playerClass or db.record[1][playerName].class
    if hasUnread then
        db.record[1][playerName].hasUnread = true
        C_Timer.After(CONFIG.TIME.UI_UPDATE_DELAY, UpdateWhisperButtonFlash)
    end
    PostDBOperation(true, false)  -- 需要清理缓存，不需要UI更新
    return playerName
end

function WhisperRecord.UpdatePlayerClass(playerName, class)
    local playerData = GetPlayerData(playerName)
    if playerData then
        playerData.class = class
        WhisperRecord.UpdateWhisperList()
    end
end

-- 删除重复的RefreshUI函数，直接使用UpdateWhisperList

function WhisperRecord.CleanOldRecords()
    local globalDB = GetWhisperGlobalDB()
    local cutoffTime = time() - (globalDB.Days * CONFIG.TIME.HOURS_PER_DAY * CONFIG.TIME.MINUTES_PER_HOUR * CONFIG.TIME.SECONDS_PER_MINUTE)

    for playerName, playerData in pairs(globalDB.record[1]) do
        if playerData.lastTime < cutoffTime then
            globalDB.record[1][playerName] = nil
        end
    end
    PostDBOperation(true, false)  -- 需要清理缓存，不需要UI更新
end

function WhisperRecord.HasUnreadMessages()
    local globalDB = GetWhisperGlobalDB()
    local records = globalDB.record[1] or {}
    for _, playerData in pairs(records) do
        if playerData.hasUnread then return true end
    end
    return false
end

-- 创建主框架
local function CreateMainFrame()
    local config = CONFIG.MAIN_FRAME
    local WanMY = Utils.CreateMovableFrame(config.name, UIParent, config.width, config.height)
    WanMY:SetPoint("CENTER", UIParent, "CENTER", 0, 70)
    WanMY:SetClampedToScreen(true)
    Utils.SetFrameBackdrop(WanMY, CONFIG.UI.BACKDROP_ALPHA)
    WanMY:Hide()

    -- 让ESC优先关闭本框体
    WanMY:SetFrameStrata("DIALOG")
    WanMY:SetToplevel(true)
    WanMY:SetPropagateKeyboardInput(false)
    table.insert(UISpecialFrames, config.name)

    WanMY.biaoti = Utils.CreateTitle(WanMY, "密语记录")
    WanMY.closeButton = Utils.CreateCloseButton(WanMY)

    return WanMY
end

local function CreateSettingsButton(WanMY)
    local shezhi = Utils.CreateButton(WanMY, "", 18, 18)
    shezhi:SetPoint("TOPLEFT", WanMY, "TOPLEFT", 4, -1.8)
    shezhi:SetHighlightTexture("Interface/Buttons/UI-Common-MouseHilight")
    shezhi.Tex = shezhi:CreateTexture(nil, "OVERLAY")
    shezhi.Tex:SetTexture("Interface/GossipFrame/BinderGossipIcon")
    shezhi.Tex:SetPoint("CENTER", 0, 0)
    shezhi.Tex:SetSize(16, 16)
    shezhi:SetScript("OnMouseDown", function(self) self.Tex:SetPoint("CENTER", -1, -1) end)
    shezhi:SetScript("OnMouseUp", function(self) self.Tex:SetPoint("CENTER", 0, 0) end)
    return shezhi
end

local function CreateSettingsPanel(WanMY)
    local config = CONFIG.MAIN_FRAME
    local shezhiF = CreateFrame("Frame", nil, WanMY.shezhi, "BackdropTemplate")
    shezhiF:SetSize(config.width, config.height)
    shezhiF:SetPoint("TOPRIGHT", WanMY, "TOPLEFT", -1, 0)
    Utils.SetFrameBackdrop(shezhiF, CONFIG.UI.SETTINGS_BACKDROP_ALPHA)
    shezhiF:Hide()

    shezhiF.closeButton = Utils.CreateCloseButton(shezhiF)
    shezhiF.biaoti = Utils.CreateTitle(shezhiF, "设置")

    return shezhiF
end

local function CreateSettingsOptions(shezhiF)
    local settingsOptions = {{key = "Open", text = "启用密语记录", y = -30}, {key = "Tips", text = "新密语提醒", y = -60}, {key = "SoundAlert", text = "声音提醒", y = -90}}
    for _, option in ipairs(settingsOptions) do
        local checkbox = Utils.CreateCheckbox(shezhiF, option.text, option.key)
        checkbox:SetPoint("TOPLEFT", shezhiF, "TOPLEFT", 10, option.y)
        shezhiF[option.key:lower()] = checkbox
    end
    return settingsOptions
end

local function CreateDaysSettings(shezhiF)
    shezhiF.tianshulabel = shezhiF:CreateFontString(nil, "OVERLAY", "GameFontNormal")
    shezhiF.tianshulabel:SetPoint("TOPLEFT", shezhiF, "TOPLEFT", 10, -150)
    shezhiF.tianshulabel:SetText("保存天数:")
    shezhiF.tianshu = CreateFrame("EditBox", nil, shezhiF)
    shezhiF.tianshu:SetSize(50, 20)
    shezhiF.tianshu:SetPoint("LEFT", shezhiF.tianshulabel, "RIGHT", 5, 0)
    shezhiF.tianshu:SetAutoFocus(false)
    shezhiF.tianshu:SetText(tostring(GetWhisperGlobalDB()["Days"]))

    -- 使用与WantinyUI相同的样式
    local leftTexture = shezhiF.tianshu:CreateTexture(nil, "BACKGROUND")
    leftTexture:SetTexture("interface/common/commonsearch")
    leftTexture:SetSize(8, 20)
    leftTexture:SetPoint("LEFT", shezhiF.tianshu, "LEFT", -5, 0)
    leftTexture:SetTexCoord(0.88, 0.95, 0.01, 0.31)

    local rightTexture = shezhiF.tianshu:CreateTexture(nil, "BACKGROUND")
    rightTexture:SetTexture("interface/common/commonsearch")
    rightTexture:SetSize(8, 20)
    rightTexture:SetPoint("RIGHT", shezhiF.tianshu, "RIGHT", 0, 0)
    rightTexture:SetTexCoord(0, 0.07, 0.338, 0.638)

    local middleTexture = shezhiF.tianshu:CreateTexture(nil, "BACKGROUND")
    middleTexture:SetTexture("interface/common/commonsearch")
    middleTexture:SetPoint("LEFT", leftTexture, "RIGHT")
    middleTexture:SetPoint("RIGHT", rightTexture, "LEFT")
    middleTexture:SetHeight(20)
    middleTexture:SetTexCoord(0, 0.8, 0.01, 0.31)

    shezhiF.tianshu:SetFontObject(ChatFontNormal)

    shezhiF.tianshu:SetScript("OnTabPressed", function(self) self:ClearFocus() end)
    shezhiF.tianshu:SetScript("OnEscapePressed", function(self) self:ClearFocus() end)
    shezhiF.tianshu:SetScript("OnEditFocusLost", function(self) self:HighlightText(0, 0) end)
    shezhiF.tianshu:SetScript("OnEditFocusGained", function(self) self:HighlightText() end)
    shezhiF.tianshu:SetScript("OnEnterPressed", function(self)
        local days = math.max(1, math.min(365, tonumber(self:GetText()) or 7))
        GetWhisperGlobalDB()["Days"] = days
        self:SetText(tostring(days))
        self:ClearFocus()
    end)
end

local function CreateClearButton(shezhiF)
    shezhiF.MIYUJILUBUT = Utils.CreateButton(shezhiF, "清空记录", 76, 20)
    shezhiF.MIYUJILUBUT:SetPoint("BOTTOM", shezhiF, "BOTTOM", 0, 10)  -- 改为底部居中
    shezhiF.MIYUJILUBUT:SetScript("OnClick", function() StaticPopup_Show("CHONGZHI_MIYUJILU") end)
end

local function SetupSettingsPanelEvents(shezhiF, settingsOptions)
    shezhiF:SetScript("OnShow", function(self)
        local db = GetWhisperGlobalDB()
        for _, option in ipairs(settingsOptions) do
            if self[option.key:lower()] then
                self[option.key:lower()]:SetChecked(db[option.key])
            end
        end
        self.tianshu:SetText(tostring(db["Days"]))
    end)
end

function WhisperRecord.CreateWhisperRecordFrame()
    local WanMY = CreateMainFrame()
    WanMY.shezhi = CreateSettingsButton(WanMY)
    WanMY.shezhiF = CreateSettingsPanel(WanMY)
    local settingsOptions = CreateSettingsOptions(WanMY.shezhiF)
    CreateDaysSettings(WanMY.shezhiF)
    CreateClearButton(WanMY.shezhiF)
    SetupSettingsPanelEvents(WanMY.shezhiF, settingsOptions)
    WanMY.shezhi:SetScript("OnClick", function() WanMY.shezhiF:SetShown(not WanMY.shezhiF:IsShown()) end)
    WhisperRecord.CreateWhisperListArea(WanMY)
    WhisperRecord.WanMY = WanMY
    return WanMY
end

function WhisperRecord.CreateWhisperListArea(WanMY)
    local config = CONFIG.MAIN_FRAME
    WanMY.nr = CreateFrame("Frame", nil, WanMY)
    WanMY.nr:SetSize(config.width - 10, config.height - 50)
    WanMY.nr:SetPoint("TOPLEFT", WanMY, "TOPLEFT", 5, -25)
    WanMY.titleDivider = WanMY:CreateTexture(nil, "ARTWORK")
    WanMY.titleDivider:SetColorTexture(CONFIG.COLORS.DIVIDER.r, CONFIG.COLORS.DIVIDER.g, CONFIG.COLORS.DIVIDER.b, CONFIG.COLORS.DIVIDER.a)
    WanMY.titleDivider:SetHeight(2)
    WanMY.titleDivider:SetPoint("TOPLEFT", WanMY, "TOPLEFT", 8, -22)
    WanMY.titleDivider:SetPoint("TOPRIGHT", WanMY, "TOPRIGHT", -8, -22)
    WanMY.chatContent = CreateFrame("Frame", nil, WanMY, "BackdropTemplate")
    WanMY.chatContent:SetSize(300, 200)
    WanMY.chatContent:SetPoint("TOPLEFT", WanMY, "TOPRIGHT", 5, 0)
    Utils.SetFrameBackdrop(WanMY.chatContent, CONFIG.UI.CHAT_CONTENT_BACKDROP_ALPHA)
    WanMY.chatContent:Hide()
    WanMY.chatContent.title = Utils.CreateTitle(WanMY.chatContent, "聊天记录", "GameFontNormal", -8)
    WanMY.chatContent.closeBtn = Utils.CreateCloseButton(WanMY.chatContent, function()
        WanMY.chatContent:Hide()
        WhisperRecord.ClearSelectedState()
    end)
    WanMY.chatContent.messageFrame = Utils.CreateMessageFrame(WanMY.chatContent, 280, 150)
    WanMY.chatContent.messageFrame:SetPoint("TOP", WanMY.chatContent, "TOP", 0, -30)
    WanMY.chatContent.hideTimer = nil
    WanMY.chatContent:SetScript("OnEnter", function(self)
        if self.hideTimer then self.hideTimer:Cancel(); self.hideTimer = nil end
    end)
    WanMY.chatContent:SetScript("OnLeave", function(self)
        if self.hideTimer then self.hideTimer:Cancel() end
        self.hideTimer = C_Timer.NewTimer(1, function()
            if self:IsShown() and not self:IsMouseOver() then
                self:Hide()
                WhisperRecord.ClearSelectedState()
            end
        end)
    end)
    WanMY.nr.Scroll = CreateFrame("ScrollFrame", nil, WanMY.nr, "UIPanelScrollFrameTemplate")
    WanMY.nr.Scroll:SetSize(config.width - 10, config.height - 50)
    WanMY.nr.Scroll:SetPoint("TOPRIGHT", WanMY.nr, "TOPRIGHT", 0, 0)
    WanMY.nr.Scroll:SetPoint("TOPLEFT", WanMY.nr, "TOPLEFT", 0, 0)
    if WanMY.nr.Scroll.ScrollBar then
        WanMY.nr.Scroll.ScrollBar:SetScale(CONFIG.UI.SCROLLBAR_SCALE)
        WanMY.nr.Scroll.ScrollBar:ClearAllPoints()
        WanMY.nr.Scroll.ScrollBar:SetPoint("TOPRIGHT", WanMY.nr.Scroll, "TOPRIGHT", 0, -18)
        WanMY.nr.Scroll.ScrollBar:SetPoint("BOTTOMRIGHT", WanMY.nr.Scroll, "BOTTOMRIGHT", 0, -10)
    end
    WanMY.nr.Scroll.child = CreateFrame("Frame", nil, WanMY.nr.Scroll)
    WanMY.nr.Scroll.child:SetSize(config.width - 30, 100)  -- 初始高度，会根据实际数据调整
    WanMY.nr.Scroll:SetScrollChild(WanMY.nr.Scroll.child)
    WanMY.nr.Scroll.child.list = {}
    -- 不再预创建列表项，改为按需创建
end

function WhisperRecord.ClearSelectedState()
    local WanMY = WhisperRecord.WanMY
    if not WanMY then return end
    for _, item in pairs(WanMY.nr.Scroll.child.list) do item.selected = false end
end

function WhisperRecord.ShowChatContent(playerName)
    if not playerName then return end
    local WanMY = WhisperRecord.WanMY
    if not WanMY then return end
    local chatContent = WanMY.chatContent
    chatContent.currentPlayer = playerName
    WhisperRecord.ClearSelectedState()
    local playerData = GetPlayerData(playerName)
    if playerData then
        local classColor = WhisperRecord.Utils.GetPlayerClassColor(playerData.class)
        chatContent.title:SetText("与 " .. playerName .. " 的聊天记录")
        chatContent.title:SetTextColor(classColor.r, classColor.g, classColor.b, 1)
    else
        chatContent.title:SetText("与 " .. playerName .. " 的聊天记录")
        chatContent.title:SetTextColor(1, 1, 1, 1)
    end
    chatContent:ClearAllPoints()
    if (select(1, WanMY:GetCenter()) or 0) < UIParent:GetWidth() / 2 then
        chatContent:SetPoint("TOPLEFT", WanMY, "TOPRIGHT", 5, 0)
    else
        chatContent:SetPoint("TOPRIGHT", WanMY, "TOPLEFT", -5, 0)
    end
    chatContent.messageFrame:Clear()
    WhisperRecord.LoadRecentMessages(playerName)
    chatContent:Show()
    SetAutoHideTimer(chatContent)
end

function WhisperRecord.LoadRecentMessages(playerName)
    local WanMY = WhisperRecord.WanMY
    if not WanMY then return end
    local playerData = GetPlayerData(playerName)
    if not playerData or not playerData.messages then return end
    local allMessages = {}
    for dateKey, messages in pairs(playerData.messages) do
        for _, msgData in ipairs(messages) do
            -- 确保消息数据包含正确的玩家名称
            msgData.playerName = playerName
            table.insert(allMessages, msgData)
        end
    end
    table.sort(allMessages, function(a, b) return a.time < b.time end)
    local startIndex = math.max(1, #allMessages - 20 + 1)
    for i = startIndex, #allMessages do
        WhisperRecord.Utils.AddFormattedMessage(WanMY.chatContent.messageFrame, allMessages[i], {history=false})
    end
    WanMY.chatContent.messageFrame:ScrollToBottom()
end

function WhisperRecord.DeletePlayerRecord(playerName)
    if not playerName then return end
    StaticPopup_Show("DELETE_WHISPER_RECORD", playerName, nil, playerName)
end

local function HasActualMessages(playerName)
    local playerData = GetPlayerData(playerName)
    if not playerData or not playerData.messages then return false end
    for _, messages in pairs(playerData.messages) do if #messages > 0 then return true end end
    return false
end
WhisperRecord.Utils.HasActualMessages = HasActualMessages

-- 按需创建列表项：有多少数据就创建多少UI
local function CreateListItems(container, count, config)
    local list = container.list or {}

    -- 如果数量匹配，直接返回
    if #list == count then return list end

    -- 清理现有的列表项
    for _, item in ipairs(list) do
        if item then item:Hide(); item:SetParent(nil) end
    end
    wipe(list)

    -- 按实际需要创建列表项
    for i = 1, count do
        local listItem = CreateFrame("Button", nil, container)
        listItem:SetSize(config.width - 30, config.itemHeight)
        listItem:SetPoint("TOPLEFT", container, "TOPLEFT", 0, -(i-1) * config.itemHeight)
        listItem:EnableMouse(true)
        listItem:RegisterForClicks("LeftButtonUp")

        -- 创建纹理背景
        for _, data in ipairs({
            {"bg", "BACKGROUND", "Interface/Buttons/UI-Listbox-Highlight2", CONFIG.UI.LIST_ITEM_BG_ALPHA},
            {"highlight", "HIGHLIGHT", "Interface/Buttons/UI-Listbox-Highlight", CONFIG.UI.LIST_ITEM_HIGHLIGHT_ALPHA}
        }) do
            local name, layer, texture, alpha = unpack(data)
            listItem[name] = listItem:CreateTexture(nil, layer)
            listItem[name]:SetAllPoints()
            listItem[name]:SetTexture(texture)
            listItem[name]:SetAlpha(alpha)
        end

        -- 创建子元素
        listItem.number = listItem:CreateFontString(nil, "OVERLAY", "GameFontNormalSmall")
        listItem.number:SetPoint("LEFT", listItem, "LEFT", 2, 0)
        listItem.number:SetJustifyH("LEFT")
        listItem.number:SetTextColor(0.7, 0.7, 0.7, 1)  -- 灰色编号

        listItem.classIcon = listItem:CreateTexture(nil, "BORDER")
        listItem.classIcon:SetPoint("LEFT", listItem.number, "RIGHT", 2, 0)
        listItem.classIcon:SetSize(config.itemHeight - 5, config.itemHeight - 5)

        listItem.name = listItem:CreateFontString(nil, "OVERLAY", "GameFontNormal")
        listItem.name:SetPoint("LEFT", listItem.classIcon, "RIGHT", 4, 0)
        listItem.name:SetJustifyH("LEFT")

        listItem.deleteBtn = CreateFrame("Button", nil, listItem)
        listItem.deleteBtn:SetSize(16, 16)
        listItem.deleteBtn:SetPoint("RIGHT", listItem, "RIGHT", 10, 0)
        listItem.deleteBtn:SetNormalTexture("Interface/Buttons/UI-Panel-MinimizeButton-Up")
        listItem.deleteBtn:SetHighlightTexture("Interface/Buttons/UI-Panel-MinimizeButton-Highlight")
        listItem.deleteBtn:SetPushedTexture("Interface/Buttons/UI-Panel-MinimizeButton-Down")
        listItem.deleteBtn:SetScript("OnClick", function(self)
            local playerName = self:GetParent().playerName
            if playerName then WhisperRecord.DeletePlayerRecord(playerName) end
        end)
        Utils.SetButtonHoverEvents(listItem.deleteBtn, nil, nil, "删除此玩家的记录")

        table.insert(list, listItem)
    end

    container.list = list
    container:SetHeight(count * config.itemHeight)
    return list
end

function WhisperRecord.UpdateWhisperList()
    local WanMY = WhisperRecord.WanMY
    if not WanMY or not WanMY:IsShown() then return end
    local globalDB = GetWhisperGlobalDB()
    local allRecords = globalDB.record[1] or {}
    local playerList = {}
    for playerName, data in pairs(allRecords) do
        if data.lastTime then
            table.insert(playerList, {
                name = playerName,
                lastTime = data.lastTime,
                class = data.class,
                hasUnread = data.hasUnread or false,
                hasMessages = HasActualMessages(playerName)
            })
        end
    end
    table.sort(playerList, function(a, b)
        return a.lastTime > b.lastTime
    end)

    -- 按需创建列表项：有多少数据就创建多少UI
    local listItems = CreateListItems(WanMY.nr.Scroll.child, #playerList, CONFIG.MAIN_FRAME)

    -- 显示所有玩家数据
    for i = 1, #playerList do
        local listItem = listItems[i]
        if listItem then
            local playerInfo = playerList[i]
            listItem.playerName = playerInfo.name

            -- 设置编号
            listItem.number:SetText(i .. ".")

            -- 检查是否为跨服玩家，如果是则显示服务器名称
            local currentRealm = GetRealmName()
            local playerName, playerRealm = strsplit("-", playerInfo.name)
            local shouldShowRealm = playerRealm and playerRealm ~= currentRealm

            listItem.name:SetText(Utils.GetDisplayName(playerInfo.name, shouldShowRealm))

            -- 设置职业图标
            local iconTexture, iconCoords = WhisperRecord.Utils.GetClassIcon(playerInfo.class)
            if iconTexture and iconCoords then
                listItem.classIcon:SetTexture(iconTexture)
                listItem.classIcon:SetTexCoord(unpack(iconCoords))
                listItem.classIcon:Show()
            else
                listItem.classIcon:Hide()
            end

            -- 设置名字颜色
            local classColor = WhisperRecord.Utils.GetPlayerClassColor(playerInfo.class)
            listItem.name:SetTextColor(classColor.r, classColor.g, classColor.b, 1)

            -- 创建未读背景（按照源代码标准）
            if not listItem.unreadBg then
                listItem.unreadBg = listItem:CreateTexture(nil, "BACKGROUND")
                listItem.unreadBg:SetAllPoints(listItem)
                listItem.unreadBg:SetColorTexture(0, 1, 0, 0.2)  -- 绿色背景，透明度0.2
            end

            if playerInfo.hasUnread then listItem.unreadBg:Show() else listItem.unreadBg:Hide() end

            if playerInfo.hasMessages then listItem.deleteBtn:Show() else listItem.deleteBtn:Hide() end

            -- 设置事件处理（每次都重新设置，确保正确）
            listItem:SetScript("OnEnter", function(self)
                if self.playerName then
                    WhisperRecord.ShowChatContent(self.playerName)
                    WhisperRecord.ClearSelectedState()
                    self.selected = true
                    -- 标记为已读并更新UI（合并操作）
                    local playerData = GetPlayerData(self.playerName)
                    if playerData then
                        playerData.hasUnread = false
                        PostDBOperation(false, true)  -- 不需要清理缓存，需要UI更新
                        -- 延迟更新列表，避免频繁刷新
                        C_Timer.After(0.05, function()
                            if WhisperRecord.WanMY and WhisperRecord.WanMY:IsShown() then
                                WhisperRecord.UpdateWhisperList()
                            end
                        end)
                    end
                end
            end)

            listItem:SetScript("OnLeave", function(self)
                local WanMY = WhisperRecord.WanMY
                if WanMY and WanMY.chatContent:IsShown() then
                    if WanMY.chatContent.hideTimer then WanMY.chatContent.hideTimer:Cancel() end
                    WanMY.chatContent.hideTimer = C_Timer.NewTimer(0.15, function()
                        if WanMY.chatContent:IsShown() and not WanMY.chatContent:IsMouseOver() then
                            WanMY.chatContent:Hide()
                            WhisperRecord.ClearSelectedState()
                        end
                    end)
                end
            end)

            listItem:SetScript("OnClick", function(self)
                if self.playerName then
                    local name = Utils.GetDisplayName(self.playerName, true)
                    if name then
                        local playerData = GetPlayerData(self.playerName)
                        if playerData and playerData.class == CONFIG.BNET_CLASS_ID then
                            local displayName = Utils.GetDisplayName(self.playerName)
                            ChatFrame_SendBNetTell(displayName)
                        else
                            ChatFrame_SendTell(name)
                        end
                    end
                end
            end)

            listItem:SetScript("OnMouseUp", function(self, button)
                if not self.playerName then return end
                if button == "RightButton" then
                    local name = Utils.GetDisplayName(self.playerName, true)
                    if name and FriendsFrame_ShowDropdown then
                        FriendsFrame_ShowDropdown(name, 1)
                    end
                end
            end)

            listItem:Show()
        end
    end
end



-- 静态弹窗对话框
StaticPopupDialogs["CHONGZHI_MIYUJILU"] = {
    text = "确定要清空所有的密语记录吗？",
    button1 = "确定",
    button2 = "取消",
    OnAccept = function()
        GetWhisperGlobalDB().record = {{}, {}}
        PostDBOperation(true, true)  -- 需要清理缓存和UI更新
        -- 延迟更新UI，避免阻塞
        C_Timer.After(CONFIG.TIME.UI_UPDATE_DELAY, function()
            if WhisperRecord.WanMY and WhisperRecord.WanMY:IsShown() then
                WhisperRecord.UpdateWhisperList()
            end
        end)
    end,
    timeout = 0,
    whileDead = true,
    hideOnEscape = true,
    preferredIndex = 3,
}

StaticPopupDialogs["DELETE_WHISPER_RECORD"] = {
    text = "确定要删除与 %s 的所有密语记录吗？",
    button1 = "确定",
    button2 = "取消",
    OnAccept = function(self, playerName)
        if playerName then
            local playerData = GetPlayerData(playerName)
            if playerData then
                GetWhisperGlobalDB().record[1][playerName] = nil
                PostDBOperation(true, true)  -- 需要清理缓存和UI更新
                -- 延迟更新UI，避免阻塞
                C_Timer.After(CONFIG.TIME.UI_UPDATE_DELAY, function()
                    if WhisperRecord.WanMY and WhisperRecord.WanMY:IsShown() then
                        WhisperRecord.UpdateWhisperList()
                    end
                end)
            end
        end
    end,
    timeout = 0,
    whileDead = true,
    hideOnEscape = true,
    preferredIndex = 3,
}



-- 主要接口函数
function WhisperRecord.ToggleWhisperRecordFrame()
    local WanMY = WhisperRecord.WanMY or WhisperRecord.CreateWhisperRecordFrame()
    if WanMY:IsShown() then WanMY:Hide() else WanMY:Show(); WhisperRecord.UpdateWhisperList() end
end

function WhisperRecord.ShowWhisperRecordFrame()
    local WanMY = WhisperRecord.WanMY or WhisperRecord.CreateWhisperRecordFrame()
    WanMY:Show()
    WhisperRecord.UpdateWhisperList()
end

function WhisperRecord.HideWhisperRecordFrame()
    local WanMY = WhisperRecord.WanMY
    if WanMY then WanMY:Hide(); WhisperRecord.ClearSelectedState() end
end

-- 注册时间戳点击处理器 - 安全的全局函数重写
local function RegisterTimestampClickHandler()
    -- 注册自定义超链接处理器
    local originalSetItemRef = SetItemRef
    SetItemRef = function(link, text, button, chatFrame)
        local success, result = pcall(function()
            if link and link:match("^wchat:copy:") then
                local messageId = link:match("^wchat:copy:(.+)$")
                if messageId and WChat.CopySingleMessage then
                    WChat:CopySingleMessage(messageId)
                    return true
                end
            end
            return false
        end)

        if success and result then
            return
        end

        -- 调用原始函数处理其他链接，带错误处理
        if originalSetItemRef then
            local success2, result2 = pcall(originalSetItemRef, link, text, button, chatFrame)
            if success2 then
                return result2
            end
        end
    end
end

-- 初始化时间戳点击功能
C_Timer.After(1, function()
    RegisterTimestampClickHandler()
end)

-- 时间戳颜色选择器（使用WoW标准颜色选择器）
function WChat:ShowTimestampColorPicker()
    local config = GetConfig()
    local currentColor = config.TimestampColor or {r = 255, g = 20, b = 147}

    -- 转换为0-1范围的颜色值
    local r, g, b = currentColor.r / 255, currentColor.g / 255, currentColor.b / 255

    -- 使用WoW标准颜色选择器
    ColorPickerFrame:SetColorRGB(r, g, b)
    ColorPickerFrame.hasOpacity = false
    ColorPickerFrame.previousValues = {r, g, b}

    -- 颜色更新函数
    local function updateColor(r, g, b)
        config.TimestampColor = {r = math.floor(r * 255 + 0.5), g = math.floor(g * 255 + 0.5), b = math.floor(b * 255 + 0.5)}
        if WChat.OnConfigChanged and WChat.OnConfigChanged.TimestampColor then
            WChat.OnConfigChanged.TimestampColor(config.TimestampColor)
        end
    end

    -- 设置颜色选择器回调
    ColorPickerFrame.func = function() updateColor(ColorPickerFrame:GetColorRGB()) end
    ColorPickerFrame.swatchFunc = ColorPickerFrame.func
    ColorPickerFrame.cancelFunc = function(prev) if prev then updateColor(prev[1], prev[2], prev[3]) end end

    -- 颜色选择器
    ColorPickerFrame:Show()
end

